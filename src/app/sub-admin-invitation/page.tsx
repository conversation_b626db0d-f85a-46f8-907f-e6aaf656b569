import { Suspense } from 'react';
import { SubAdminInvitationModule } from '@/modules/sub-admin/invitation/components/sub-admin-invitation-page';

function LoadingFallback() {
  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="text-center">
        <div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-4 border-gray-300 border-t-blue-600"></div>
        <p className="text-gray-600">Loading...</p>
      </div>
    </div>
  );
}

export default function SubAdminInvitationPage() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <SubAdminInvitationModule />
    </Suspense>
  );
}
