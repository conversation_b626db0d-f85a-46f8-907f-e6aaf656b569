import { apiClient } from '@/lib/api-client';
import { useMutation, useQueryClient } from '@tanstack/react-query';

interface ResendInvitationResponse {
  statusCode: number;
  message: string;
  data: any;
  timestamp: string;
  path: string;
  method: string;
}

const resendInvitationApi = async (id: number): Promise<ResendInvitationResponse> => {
  const response = await apiClient.put(`/employer/sub-admin-invitations/${id}/resend`);
  return response as unknown as ResendInvitationResponse;
};

export const useResendInvitation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => resendInvitationApi(id),
    onSuccess: () => {
      // Invalidate invitations query to refresh the list
      queryClient.invalidateQueries({ queryKey: ['invitations-list'] });
    },
  });
};
