import { apiClient } from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';

export type InvitationStatus = 'PENDING' | 'ACCEPTED' | 'EXPIRED' | 'REJECTED';
export type InvitationType = 'EXISTING_EMPLOYEE' | 'NEW_USER';

export interface Invitation {
  id: number;
  companyId: string;
  email: string;
  invitationType: InvitationType;
  employeeId: string | null;
  status: InvitationStatus;
  invitedBy: string;
  expiresAt: string;
  acceptedAt: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface InvitationsResponse {
  statusCode: number;
  message: string;
  data: Invitation[];
  meta: {
    total: number;
    lastPage: number;
    currentPage: number;
    perPage: number;
    prev: number | null;
    next: number | null;
  };
  timestamp: string;
  path: string;
  method: string;
}

export interface InvitationsFilters {
  page?: number;
  limit?: number;
  search?: string;
  status?: InvitationStatus | 'all';
}

const fetchInvitations = async (filters: InvitationsFilters = {}): Promise<InvitationsResponse> => {
  const params = new URLSearchParams();

  // Add pagination parameters
  if (filters.page) {
    params.append('page', filters.page.toString());
  }
  if (filters.limit) {
    params.append('limit', filters.limit.toString());
  }

  // Add filter parameters
  if (filters.search && filters.search.trim()) {
    params.append('search', filters.search.trim());
  }
  if (filters.status && filters.status !== 'all') {
    params.append('status', filters.status);
  }

  const queryString = params.toString();
  const url = queryString ? `/employer/sub-admin-invitations?${queryString}` : '/employer/sub-admin-invitations';

  const response = await apiClient.get(url);

  // The API client's response interceptor returns response.data,
  // so response is already the API response body
  return response as unknown as InvitationsResponse;
};

export const useInvitations = (filters: InvitationsFilters = {}) => {
  return useQuery({
    queryKey: ['invitations-list', filters],
    queryFn: () => fetchInvitations(filters),
  });
};
