'use client';

import { Mail } from 'lucide-react';
import { InvitesListTable } from './components';
import Header from '@/components/reusable-component/header';

export const InvitesModule = () => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Header 
          title="Invitations" 
          description="Manage sub-admin invitations and track their status" 
        />
      </div>

      {/* Invitations List */}
      <InvitesListTable />
    </div>
  );
};
