import { Badge } from '@/components/ui/badge';
import { TableColumn, TableActionItem } from '@/components/table-component/data-table';
import { Invitation, InvitationStatus } from '../api';
import { format } from 'date-fns';
import { Send } from 'lucide-react';

// Status badge configuration
const getStatusBadge = (status: InvitationStatus) => {
  switch (status) {
    case 'PENDING':
      return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>;
    case 'ACCEPTED':
      return <Badge variant="secondary" className="bg-green-100 text-green-800">Accepted</Badge>;
    case 'EXPIRED':
      return <Badge variant="secondary" className="bg-red-100 text-red-800">Expired</Badge>;
    case 'REJECTED':
      return <Badge variant="secondary" className="bg-gray-100 text-gray-800">Rejected</Badge>;
    default:
      return <Badge variant="secondary">{status}</Badge>;
  }
};

// Invitation type badge configuration
const getInvitationTypeBadge = (type: string) => {
  switch (type) {
    case 'EXISTING_EMPLOYEE':
      return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Existing Employee</Badge>;
    case 'NEW_USER':
      return <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">New User</Badge>;
    default:
      return <Badge variant="outline">{type}</Badge>;
  }
};

// Format date helper
const formatDate = (dateString: string | null) => {
  if (!dateString) return '-';
  try {
    return format(new Date(dateString), 'MMM dd, yyyy HH:mm');
  } catch {
    return dateString;
  }
};

export const invitationColumns: TableColumn<Invitation>[] = [
  {
    key: 'email',
    header: 'Email',
    render: (invitation: Invitation) => (
      <div className="font-medium text-gray-900">{invitation.email}</div>
    ),
  },
  {
    key: 'invitationType',
    header: 'Type',
    render: (invitation: Invitation) => getInvitationTypeBadge(invitation.invitationType),
  },
  {
    key: 'status',
    header: 'Status',
    render: (invitation: Invitation) => getStatusBadge(invitation.status),
  },
  {
    key: 'invitedBy',
    header: 'Invited By',
    render: (invitation: Invitation) => (
      <div className="text-gray-600">{invitation.invitedBy}</div>
    ),
  },
  {
    key: 'createdAt',
    header: 'Invited At',
    render: (invitation: Invitation) => (
      <div className="text-gray-600">{formatDate(invitation.createdAt)}</div>
    ),
  },
  {
    key: 'expiresAt',
    header: 'Expires At',
    render: (invitation: Invitation) => (
      <div className="text-gray-600">{formatDate(invitation.expiresAt)}</div>
    ),
  },
  {
    key: 'acceptedAt',
    header: 'Accepted At',
    render: (invitation: Invitation) => (
      <div className="text-gray-600">{formatDate(invitation.acceptedAt)}</div>
    ),
  },
];

export const getInvitationActions = (
  onResendInvitation: (id: number) => void
): TableActionItem<Invitation>[] => [
  {
    label: 'Resend Invitation',
    icon: <Send className="h-4 w-4" />,
    onClick: (invitation: Invitation) => onResendInvitation(invitation.id),
    className: 'text-blue-600',
    show: (invitation: Invitation) => invitation.status === 'PENDING',
  },
];
