'use client';

import { useState, useEffect } from 'react';
import { Search, Mail } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useInvitations, useResendInvitation, type InvitationStatus } from '../api';
import { invitationColumns, getInvitationActions } from './invitation-columns';
import { DataTable } from '@/components/table-component/data-table';
import { toast } from 'sonner';

export const InvitesListTable = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<InvitationStatus | 'all'>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Debounce search term to avoid too many API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Fetch invitations using the API hook
  const {
    data: invitationsData,
    isLoading,
    error,
  } = useInvitations({
    page: currentPage,
    limit: itemsPerPage,
    search: debouncedSearchTerm,
    status: statusFilter,
  });

  const invitations = invitationsData?.data || [];
  const totalItems = invitationsData?.meta.total || 0;
  const totalPages = invitationsData?.meta.lastPage || 1;

  // Resend invitation mutation
  const resendMutation = useResendInvitation();

  // Reset pagination when filters change
  const handleFilterChange = () => {
    setCurrentPage(1);
  };

  // Handle resend invitation
  const handleResendInvitation = async (id: number) => {
    try {
      await resendMutation.mutateAsync(id);
      toast.success('Invitation resent successfully');
    } catch (error: any) {
      toast.error(error?.message || 'Failed to resend invitation');
    }
  };

  // Define table actions
  const actions = getInvitationActions(handleResendInvitation);

  return (
    <Card>
      <CardContent className="p-6">
        {/* Filters */}
        <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          {/* Search */}
          <div className="relative max-w-sm flex-1">
            <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Search by email..."
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                handleFilterChange();
              }}
              className="pl-10"
            />
          </div>

          {/* Status Filter */}
          <div className="flex gap-4">
            <Select
              value={statusFilter}
              onValueChange={(value: InvitationStatus | 'all') => {
                setStatusFilter(value);
                handleFilterChange();
              }}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="PENDING">Pending</SelectItem>
                <SelectItem value="ACCEPTED">Accepted</SelectItem>
                <SelectItem value="EXPIRED">Expired</SelectItem>
                <SelectItem value="REJECTED">Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* DataTable */}
        <DataTable
          columns={invitationColumns}
          data={invitations}
          meta={{
            total: totalItems,
            lastPage: totalPages,
            currentPage: currentPage,
          }}
          onPageChange={setCurrentPage}
          itemsPerPage={itemsPerPage}
          onItemsPerPageChange={(newItemsPerPage: number) => {
            setItemsPerPage(newItemsPerPage);
            setCurrentPage(1);
          }}
          actions={actions}
          isLoading={isLoading}
          error={error ? 'Error loading invitations. Please try again.' : null}
          emptyState={{
            icon: <Mail className="h-12 w-12 text-gray-400" />,
            title: 'No invitations found',
            description: 'No invitations match your search criteria.',
          }}
          getRowKey={(invitation) => invitation.id.toString()}
        />
      </CardContent>
    </Card>
  );
};
