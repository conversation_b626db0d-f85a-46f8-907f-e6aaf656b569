'use client';

import { useQuery } from '@tanstack/react-query';
import { apiClient } from '@/lib/api-client';
import {
  PayrollSettings,
  PayrollSettingsGetApiResponse,
  PayrollSettingsApiData,
  PayrollFrequency,
} from '../types';

const PAYROLL_SETTINGS_KEYS = {
  all: ['payroll-settings'] as const,
  byCompany: (companyId: string) => [...PAYROLL_SETTINGS_KEYS.all, companyId] as const,
};

// Transform API data to local PayrollSettings format
const transformApiDataToSettings = (data: PayrollSettingsApiData): PayrollSettings => {
  // Map API interval back to form frequency
  let payrollFrequency: (typeof PayrollFrequency)[keyof typeof PayrollFrequency];
  switch (data.interval) {
    case 'WEEKLY':
      payrollFrequency = PayrollFrequency.WEEKLY;
      break;
    case 'BIWEEKLY':
      payrollFrequency = PayrollFrequency.BI_WEEKLY;
      break;
    case 'MONTHLY':
      payrollFrequency = PayrollFrequency.MONTHLY;
      break;
    default:
      payrollFrequency = PayrollFrequency.MONTHLY;
  }

  return {
    id: data.id,
    payrollFrequency,
    payrollDay: data.dayNumber.toString(),
    startDate: new Date(data.effectiveFrom),
    employerId: data.companyId,
    createdAt: new Date(data.createdAt),
    updatedAt: new Date(data.updatedAt),
  };
};

// API function for fetching payroll settings
const fetchPayrollSettings = async (companyId: string): Promise<PayrollSettings | null> => {
  try {
    const response = (await apiClient.get('/pay-cycle-settings')) as PayrollSettingsGetApiResponse;

    if (response && response.statusCode === 200 && response.data) {
      return transformApiDataToSettings(response.data);
    }

    return null;
  } catch (error) {
    // If 404 or no data found, return null (this is expected for new companies)
    if ((error as any)?.response?.status === 404) {
      return null;
    }
    console.log('Error fetching payroll settings:', error);
    return null;
  }
};

export const useGetPayrollSettings = (companyId: string) => {
  return useQuery({
    queryKey: PAYROLL_SETTINGS_KEYS.byCompany(companyId),
    queryFn: () => fetchPayrollSettings(companyId),
    enabled: !!companyId,
  });
};
