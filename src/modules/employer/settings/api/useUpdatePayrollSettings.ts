'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/lib/api-client';
import {
  PayrollSettings,
  PayrollSettingsForm,
  PayrollSettingsUpdatePayload,
  PayrollSettingsApiData,
  PayrollFrequency,
  ApiPayrollInterval,
} from '../types';

const PAYROLL_SETTINGS_KEYS = {
  all: ['payroll-settings'] as const,
  byCompany: (companyId: string) => [...PAYROLL_SETTINGS_KEYS.all, companyId] as const,
};

// Transform form data to PUT API payload (no companyId needed for update)
const transformFormToUpdatePayload = (data: PayrollSettingsForm): PayrollSettingsUpdatePayload => {
  // Map form frequency to API interval
  let interval: 'WEEKLY' | 'BIWEEKLY' | 'MONTHLY';
  switch (data.payrollFrequency) {
    case PayrollFrequency.WEEKLY:
      interval = ApiPayrollInterval.WEEKLY;
      break;
    case PayrollFrequency.BI_WEEKLY:
      interval = ApiPayrollInterval.BI_WEEKLY;
      break;
    case PayrollFrequency.MONTHLY:
      interval = ApiPayrollInterval.MONTHLY;
      break;
    default:
      interval = ApiPayrollInterval.MONTHLY;
  }

  return {
    interval,
    dayNumber: parseInt(data.payrollDay),
    effectiveFrom: data.startDate.toISOString(),
  };
};

// Transform API response back to PayrollSettings
const transformApiDataToSettings = (data: PayrollSettingsApiData): PayrollSettings => {
  // Map API interval back to form frequency
  let payrollFrequency: (typeof PayrollFrequency)[keyof typeof PayrollFrequency];
  switch (data.interval) {
    case 'WEEKLY':
      payrollFrequency = PayrollFrequency.WEEKLY;
      break;
    case 'BIWEEKLY':
      payrollFrequency = PayrollFrequency.BI_WEEKLY;
      break;
    case 'MONTHLY':
      payrollFrequency = PayrollFrequency.MONTHLY;
      break;
    default:
      payrollFrequency = PayrollFrequency.MONTHLY;
  }

  return {
    id: data.id,
    payrollFrequency,
    payrollDay: data.dayNumber.toString(),
    startDate: new Date(data.effectiveFrom),
    employerId: data.companyId,
    createdAt: new Date(data.createdAt),
    updatedAt: new Date(data.updatedAt),
  };
};

// API function for updating payroll settings
const updatePayrollSettings = async (data: PayrollSettingsForm): Promise<PayrollSettings> => {
  const payload = transformFormToUpdatePayload(data);

  const response = (await apiClient.put('/pay-cycle-settings', payload)) as {
    data: PayrollSettingsApiData;
  };

  return transformApiDataToSettings(response.data);
};

export const useUpdatePayrollSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ companyId, data }: { companyId: string; data: PayrollSettingsForm }) =>
      updatePayrollSettings(data),
    onSuccess: (_, { companyId }) => {
      queryClient.invalidateQueries({
        queryKey: PAYROLL_SETTINGS_KEYS.byCompany(companyId),
      });
    },
  });
};
