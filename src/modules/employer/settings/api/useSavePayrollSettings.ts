'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/lib/api-client';
import {
  PayrollSettings,
  PayrollSettingsForm,
  PayrollSettingsApiPayload,
  PayrollFrequency,
  ApiPayrollInterval,
} from '../types';

const PAYROLL_SETTINGS_KEYS = {
  all: ['payroll-settings'] as const,
  byCompany: (companyId: string) => [...PAYROLL_SETTINGS_KEYS.all, companyId] as const,
};

// Payload transformation functions
const transformFormToApiPayload = (
  companyId: string,
  data: PayrollSettingsForm
): PayrollSettingsApiPayload => {
  // Map form frequency to API interval
  let interval: 'WEEKLY' | 'BIWEEKLY' | 'MONTHLY';
  switch (data.payrollFrequency) {
    case PayrollFrequency.WEEKLY:
      interval = ApiPayrollInterval.WEEKLY;
      break;
    case PayrollFrequency.BI_WEEKLY:
      interval = ApiPayrollInterval.BI_WEEKLY;
      break;
    case PayrollFrequency.MONTHLY:
      interval = ApiPayrollInterval.MONTHLY;
      break;
    default:
      interval = ApiPayrollInterval.MONTHLY;
  }

  return {
    companyId,
    interval,
    dayNumber: parseInt(data.payrollDay),
    effectiveFrom: data.startDate.toISOString(),
  };
};

const transformApiDataToSettings = (data: any): PayrollSettings => {
  // Map API interval back to form frequency
  let payrollFrequency: (typeof PayrollFrequency)[keyof typeof PayrollFrequency];
  switch (data.interval) {
    case 'WEEKLY':
      payrollFrequency = PayrollFrequency.WEEKLY;
      break;
    case 'BIWEEKLY':
      payrollFrequency = PayrollFrequency.BI_WEEKLY;
      break;
    case 'MONTHLY':
      payrollFrequency = PayrollFrequency.MONTHLY;
      break;
    default:
      payrollFrequency = PayrollFrequency.MONTHLY;
  }

  return {
    id: data.id,
    payrollFrequency,
    payrollDay: data.dayNumber.toString(),
    startDate: new Date(data.effectiveFrom),
    employerId: data.companyId,
    createdAt: data.createdAt ? new Date(data.createdAt) : undefined,
    updatedAt: data.updatedAt ? new Date(data.updatedAt) : undefined,
  };
};

// API function for saving/creating payroll settings
const savePayrollSettings = async (
  companyId: string,
  data: PayrollSettingsForm
): Promise<PayrollSettings> => {
  const payload = transformFormToApiPayload(companyId, data);
  const response = (await apiClient.post(`/pay-cycle-settings`, payload)) as { data: any };
  return transformApiDataToSettings(response.data);
};

export const useSavePayrollSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ companyId, data }: { companyId: string; data: PayrollSettingsForm }) =>
      savePayrollSettings(companyId, data),
    onSuccess: (_, { companyId }) => {
      queryClient.invalidateQueries({
        queryKey: PAYROLL_SETTINGS_KEYS.byCompany(companyId),
      });
    },
  });
};
