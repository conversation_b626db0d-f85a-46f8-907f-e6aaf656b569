'use client';

import Header from '@/components/reusable-component/header';
import { PayrollSettingsFormComponent } from './components';
import { useSavePayrollSettings } from './api/useSavePayrollSettings';
import { useGetPayrollSettings } from './api/useGetPayrollSettings';
import { useUpdatePayrollSettings } from './api/useUpdatePayrollSettings';
import { useAuthStore } from '@/store/auth-store';
import type { PayrollSettingsForm } from './types';
import { toast } from '@/lib/toast';

export const SettingsModule = () => {
  const { companyDetails } = useAuthStore();
  const companyId = companyDetails?.id;

  const savePayrollSettings = useSavePayrollSettings();
  const updatePayrollSettings = useUpdatePayrollSettings();
  const { data: existingSettings, isLoading: isLoadingSettings } = useGetPayrollSettings(
    companyId || ''
  );

  const handleSavePayrollSettings = async (data: PayrollSettingsForm) => {
    if (!companyId) {
      toast.error('No company ID found');
      return;
    }

    console.log(data);

    try {
      // Use UPDATE if existing settings, CREATE if new
      if (existingSettings?.id) {
        await updatePayrollSettings.mutateAsync({
          companyId,
          data,
        });
        toast.success('Payroll settings updated successfully');
      } else {
        await savePayrollSettings.mutateAsync({
          companyId,
          data,
        });
        toast.success('Payroll settings saved successfully');
      }
    } catch (error) {
      toast.error(error);
    }
  };

  if (!companyId) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Header title="Settings" description="Manage your payroll settings" />
        </div>
        <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-4">
          <p className="text-sm text-yellow-800">
            Unable to save settings. Company information is not available.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Header title="Settings" description="Manage your payroll settings" />
      </div>

      {/* Payroll Settings Form */}
      <PayrollSettingsFormComponent
        initialData={
          existingSettings
            ? {
                payrollFrequency: existingSettings.payrollFrequency,
                payrollDay: existingSettings.payrollDay,
                startDate: existingSettings.startDate,
              }
            : undefined
        }
        onSubmit={handleSavePayrollSettings}
        isLoading={
          savePayrollSettings.isPending || updatePayrollSettings.isPending || isLoadingSettings
        }
        isUpdating={!!existingSettings?.id}
      />
    </div>
  );
};
