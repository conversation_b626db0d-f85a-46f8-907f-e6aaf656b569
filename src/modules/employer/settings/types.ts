import * as z from 'zod';

export const PayrollFrequency = {
  WEEKLY: 'weekly',
  BI_WEEKLY: 'bi-weekly',
  MONTHLY: 'monthly',
} as const;

// API interval mapping
export const ApiPayrollInterval = {
  WEEKLY: 'WEEKLY',
  BI_WEEKLY: 'BIWEEKLY',
  MONTHLY: 'MONTHLY',
} as const;

export type PayrollFrequencyType = (typeof PayrollFrequency)[keyof typeof PayrollFrequency];

export const payrollSettingsSchema = z.object({
  payrollFrequency: z
    .enum(['weekly', 'bi-weekly', 'monthly'])
    .refine((value) => ['weekly', 'bi-weekly', 'monthly'].includes(value), {
      message: 'Please select a payroll frequency.',
    }),
  payrollDay: z.string().min(1, 'Please select a payroll day.'),
  startDate: z.date({ message: 'Please select a start date.' }),
});

export type PayrollSettingsForm = z.infer<typeof payrollSettingsSchema>;

export interface PayrollDayOption {
  value: string;
  label: string;
}

export interface PayrollSettings {
  id?: string;
  payrollFrequency: PayrollFrequencyType;
  payrollDay: string;
  startDate: Date;
  employerId?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

// API payload types
export interface PayrollSettingsApiPayload {
  companyId: string;
  interval: 'WEEKLY' | 'BIWEEKLY' | 'MONTHLY';
  dayNumber: number;
  effectiveFrom: string; // ISO string
}

// GET API response structure
export interface PayrollSettingsGetApiResponse {
  statusCode: number;
  message: string;
  data: {
    id: string;
    companyId: string;
    interval: 'WEEKLY' | 'BIWEEKLY' | 'MONTHLY';
    dayNumber: number;
    isActive: boolean;
    effectiveFrom: string;
    createdAt: string;
    updatedAt: string;
    createdById: string;
    updatedById: string | null;
  };
  timestamp: string;
  path: string;
  method: string;
}

// API response for individual payroll setting data
export interface PayrollSettingsApiData {
  id: string;
  companyId: string;
  interval: 'WEEKLY' | 'BIWEEKLY' | 'MONTHLY';
  dayNumber: number;
  isActive: boolean;
  effectiveFrom: string;
  createdAt: string;
  updatedAt: string;
  createdById: string;
  updatedById: string | null;
}

// PUT API payload (no companyId needed)
export interface PayrollSettingsUpdatePayload {
  interval: 'WEEKLY' | 'BIWEEKLY' | 'MONTHLY';
  dayNumber: number;
  effectiveFrom: string;
}
