'use client';

import { useForm } from 'react-hook-form';
import { useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { Save, Calendar } from 'lucide-react';
import { format } from 'date-fns';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';

import type { PayrollSettingsForm, PayrollFrequencyType } from '../types';
import { payrollSettingsSchema, PayrollFrequency } from '../types';
import { getPayrollDayOptions, getPayrollDayLabel, getPayrollDayDescription } from '../utils';

interface PayrollSettingsFormProps {
  initialData?: Partial<PayrollSettingsForm>;
  onSubmit: (data: PayrollSettingsForm) => Promise<void> | void;
  isLoading?: boolean;
  isUpdating?: boolean;
}

export const PayrollSettingsFormComponent = ({
  initialData,
  onSubmit,
  isLoading = false,
  isUpdating = false,
}: PayrollSettingsFormProps) => {
  const form = useForm<PayrollSettingsForm>({
    resolver: zodResolver(payrollSettingsSchema),
    defaultValues: {
      payrollFrequency: initialData?.payrollFrequency ?? PayrollFrequency.MONTHLY,
      payrollDay: initialData?.payrollDay ?? '1',
      startDate: initialData?.startDate,
    },
  });

  const payrollFrequency = form.watch('payrollFrequency') as PayrollFrequencyType;

  // Reset form when initialData changes (e.g., when API data loads)
  useEffect(() => {
    if (initialData) {
      form.reset({
        payrollFrequency: initialData.payrollFrequency ?? PayrollFrequency.MONTHLY,
        payrollDay: initialData.payrollDay ?? '1',
        startDate: initialData.startDate,
      });
    }
  }, [initialData, form]);

  const handleSubmit = async (data: PayrollSettingsForm) => {
    try {
      await onSubmit(data);
    } catch (error) {
      console.error('Error saving payroll settings:', error);
    }
  };

  return (
    <Card className="!py-0 !pt-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="text-primary h-5 w-5" />
          Payroll Cycle Configuration
        </CardTitle>
        <CardDescription>
          Set up your company payroll cycle to enable automatic repayment generation
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6 p-6 sm:p-8">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
            {/* Form Fields Grid - Responsive Layout */}
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
              <FormField
                control={form.control}
                name="payrollFrequency"
                render={({ field }) => (
                  <FormItem className="">
                    <FormLabel className="text-sm font-medium">Payroll Frequency</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className="!h-11 w-full min-w-[200px]">
                          <SelectValue placeholder="Select payroll frequency" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value={PayrollFrequency.WEEKLY}>Weekly</SelectItem>
                        <SelectItem value={PayrollFrequency.BI_WEEKLY}>
                          Bi-weekly (Every 2 weeks)
                        </SelectItem>
                        <SelectItem value={PayrollFrequency.MONTHLY}>Monthly</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription className="text-muted-foreground text-xs">
                      How often you process payroll for your employees
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="payrollDay"
                render={({ field }) => (
                  <FormItem className="">
                    <FormLabel className="text-sm font-medium">
                      {getPayrollDayLabel(payrollFrequency)}
                    </FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className="!h-11 w-full min-w-[200px]">
                          <SelectValue placeholder="Select day" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {getPayrollDayOptions(payrollFrequency).map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription className="text-muted-foreground text-xs">
                      {getPayrollDayDescription(payrollFrequency)}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="startDate"
                render={({ field }) => (
                  <FormItem className="md:col-span-2 lg:col-span-1">
                    <FormLabel className="text-sm font-medium">Payroll Start Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              '!h-11 w-full min-w-[200px] justify-start pl-3 text-left font-normal',
                              !field.value && 'text-muted-foreground'
                            )}
                          >
                            {field.value ? format(field.value, 'PPP') : <span>Pick a date</span>}
                            <Calendar className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <CalendarComponent
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date: Date) =>
                            date < new Date() || date < new Date('1900-01-01')
                          }
                        />
                      </PopoverContent>
                    </Popover>
                    <FormDescription className="text-muted-foreground text-xs">
                      Select the date when this payroll configuration takes effect
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Submit Button */}
            <div className="flex justify-end">
              <Button
                type="submit"
                className="h-11 min-w-[160px] bg-[#0A8080] hover:bg-[#0A8080]/90"
                disabled={isLoading}
              >
                <Save className="mr-2 h-4 w-4" />
                {isLoading
                  ? isUpdating
                    ? 'Updating...'
                    : 'Saving...'
                  : isUpdating
                    ? 'Update Payroll Settings'
                    : 'Save Payroll Settings'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};
