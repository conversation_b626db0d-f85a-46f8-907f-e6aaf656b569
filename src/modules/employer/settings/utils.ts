import type { PayrollFrequencyType, PayrollDayOption } from './types';
import { PayrollFrequency } from './types';

export const getPayrollDayOptions = (frequency: PayrollFrequencyType): PayrollDayOption[] => {
  switch (frequency) {
    case PayrollFrequency.MONTHLY:
      return Array.from({ length: 28 }, (_, i) => {
        const day = i + 1;
        const suffix = day === 1 ? 'st' : day === 2 ? 'nd' : day === 3 ? 'rd' : 'th';
        return {
          value: day.toString(),
          label: `${day}${suffix} of the month`,
        };
      });

    case PayrollFrequency.WEEKLY:
      return [
        { value: '1', label: 'Monday' },
        { value: '2', label: 'Tuesday' },
        { value: '3', label: 'Wednesday' },
        { value: '4', label: 'Thursday' },
        { value: '5', label: 'Friday' },
      ];

    case PayrollFrequency.BI_WEEKLY:
      return Array.from({ length: 14 }, (_, i) => {
        const day = i + 1;
        const suffix = day === 1 ? 'st' : day === 2 ? 'nd' : day === 3 ? 'rd' : 'th';
        return {
          value: day.toString(),
          label: `${day}${suffix}`,
        };
      });

    default:
      return [];
  }
};

export const getPayrollDayLabel = (frequency: PayrollFrequencyType): string => {
  switch (frequency) {
    case PayrollFrequency.MONTHLY:
      return 'Payroll Day of Month';
    case PayrollFrequency.WEEKLY:
      return 'Payroll Day of Week';
    case PayrollFrequency.BI_WEEKLY:
      return 'First Payroll Day';
    default:
      return 'Payroll Day';
  }
};

export const getPayrollDayDescription = (frequency: PayrollFrequencyType): string => {
  switch (frequency) {
    case PayrollFrequency.MONTHLY:
      return 'Day of the month when payroll is processed';
    case PayrollFrequency.WEEKLY:
      return 'Day of the week when payroll is processed';
    case PayrollFrequency.BI_WEEKLY:
      return 'Starting day for bi-weekly payroll cycle';
    default:
      return 'Select the day for payroll processing';
  }
};
