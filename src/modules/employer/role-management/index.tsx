'use client';

import { useState } from 'react';
import { Plus, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { usePermissions } from '@/lib/permissions';
import { InviteAdminModal } from './components/invite-admin-modal';
import Header from '@/components/reusable-component/header';
import { DataTable } from '@/components/table-component/data-table';
import { useSubAdmins } from './api/useSubAdmins';
import { subAdminColumns, getSubAdminActions } from './components/sub-admin-columns';
import { SuspendSubAdminDialog } from './components/suspend-sub-admin-dialog';
import { ActivateSubAdminDialog } from './components/activate-sub-admin-dialog';
import type { SubAdmin } from './api/useSubAdmins';

export const RoleManagementModule = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);

  // Dialog states
  const [suspendDialogOpen, setSuspendDialogOpen] = useState(false);
  const [activateDialogOpen, setActivateDialogOpen] = useState(false);
  const [selectedSubAdmin, setSelectedSubAdmin] = useState<SubAdmin | null>(null);

  const { hasPermission } = usePermissions();

  const {
    data: subAdminsData,
    isLoading,
    error,
  } = useSubAdmins({ page: currentPage, limit: itemsPerPage });

  // Dialog handlers
  const handleSuspendSubAdmin = (subAdmin: SubAdmin) => {
    setSelectedSubAdmin(subAdmin);
    setSuspendDialogOpen(true);
  };

  const handleActivateSubAdmin = (subAdmin: SubAdmin) => {
    setSelectedSubAdmin(subAdmin);
    setActivateDialogOpen(true);
  };

  const handleToggleStatus = (id: string, newStatus: boolean) => {
    const subAdmin = subAdminsData?.data.find((sa) => sa.id === id);
    if (!subAdmin) return;

    if (newStatus) {
      handleActivateSubAdmin(subAdmin);
    } else {
      handleSuspendSubAdmin(subAdmin);
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
  };

  // Calculate statistics
  const totalSubAdmins = subAdminsData?.meta.total || 0;
  const activeSubAdmins = subAdminsData?.activeSubAdminsCount || 0;
  const suspendedSubAdmins = subAdminsData?.suspendedSubAdminsCount || 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Header title="Role Management" description="Manage admin roles and permissions" />

        {hasPermission('roles.invite') && (
          <Button
            onClick={() => setIsInviteModalOpen(true)}
            className="bg-primary hover:bg-primary/90 h-10 px-6"
          >
            <Plus className="mr-2 h-4 w-4" />
            Invite Admin
          </Button>
        )}
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardContent className="">
            <div className="flex items-center">
              <div className="bg-primary/10 flex h-12 w-12 items-center justify-center rounded-lg">
                <Shield className="text-primary h-6 w-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Sub-Admins</p>
                <p className="text-2xl font-bold text-gray-900">{totalSubAdmins}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="">
            <div className="flex items-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100">
                <Shield className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active</p>
                <p className="text-2xl font-bold text-gray-900">{activeSubAdmins}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="">
            <div className="flex items-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-red-100">
                <Shield className="h-6 w-6 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Suspended</p>
                <p className="text-2xl font-bold text-gray-900">{suspendedSubAdmins}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Sub-Admin List */}
      <Card>
        <CardContent>
          <DataTable
            columns={subAdminColumns}
            data={subAdminsData?.data || []}
            meta={{
              total: subAdminsData?.meta.total || 0,
              lastPage: subAdminsData?.meta.lastPage || 1,
              currentPage: subAdminsData?.meta.currentPage || 1,
            }}
            onPageChange={handlePageChange}
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            actions={getSubAdminActions(handleToggleStatus)}
            getRowKey={(subAdmin) => subAdmin.id}
            isLoading={isLoading}
            error={error?.message}
            emptyState={{
              icon: <Shield className="h-12 w-12 text-gray-400" />,
              title: 'No sub-admins found',
              description: 'Start by inviting a sub-admin to manage your organization.',
            }}
          />
        </CardContent>
      </Card>

      {/* Invite Admin Modal */}
      {hasPermission('roles.invite') && (
        <InviteAdminModal isOpen={isInviteModalOpen} onClose={() => setIsInviteModalOpen(false)} />
      )}

      {/* Suspend Sub-Admin Dialog */}
      <SuspendSubAdminDialog
        isOpen={suspendDialogOpen}
        onClose={() => setSuspendDialogOpen(false)}
        subAdmin={selectedSubAdmin}
      />

      {/* Activate Sub-Admin Dialog */}
      <ActivateSubAdminDialog
        isOpen={activateDialogOpen}
        onClose={() => setActivateDialogOpen(false)}
        subAdmin={selectedSubAdmin}
      />
    </div>
  );
};
