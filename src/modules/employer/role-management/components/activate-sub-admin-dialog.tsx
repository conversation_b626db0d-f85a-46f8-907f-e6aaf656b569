'use client';

import { useState } from 'react';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { toast } from '@/lib/toast';
import { useReactivateSubAdmin } from '../api/useReactivateSubAdmin';
import { SubAdmin } from '../api/useSubAdmins';

interface ActivateSubAdminDialogProps {
  isOpen: boolean;
  onClose: () => void;
  subAdmin: SubAdmin | null;
}

export const ActivateSubAdminDialog = ({
  isOpen,
  onClose,
  subAdmin,
}: ActivateSubAdminDialogProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const reactivateMutation = useReactivateSubAdmin();

  const handleActivate = async () => {
    if (!subAdmin) return;

    setIsSubmitting(true);
    try {
      await reactivateMutation.mutateAsync(subAdmin.id);

      toast.success(`${subAdmin.firstName} ${subAdmin.lastName} has been activated successfully.`);
      onClose();
    } catch (error: any) {
      toast.error(
        error?.response?.data?.message || 'Failed to activate sub-admin. Please try again.'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Activate Sub-Admin</DialogTitle>
          <DialogDescription>
            Are you sure you want to activate{' '}
            <strong>
              {subAdmin?.firstName} {subAdmin?.lastName}
            </strong>
            ? This will restore their access to the system.
          </DialogDescription>
        </DialogHeader>

        <DialogFooter className="gap-2">
          <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button type="button" onClick={handleActivate} disabled={isSubmitting}>
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Activate Sub-Admin
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
