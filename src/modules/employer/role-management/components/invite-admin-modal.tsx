'use client';

import type React from 'react';
import { useState, useEffect } from 'react';
import { Mail, UserPlus, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Employee as FullEmployee,
  EmployeesFilters,
  useEmployees,
} from '../../employee/api/useEmployees';
import {
  EmployeeEmailSelect,
  EmployeeEmailSelectOption,
} from '@/components/ui/employee-email-select';
import { useSendInvitation } from '../api/useSendInvitation';
import { toast } from '@/lib/toast';

interface InviteAdminModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function InviteAdminModal({ isOpen, onClose }: InviteAdminModalProps) {
  const [email, setEmail] = useState('');
  const [selectedEmployee, setSelectedEmployee] = useState<FullEmployee | null>(null);
  const [emailError, setEmailError] = useState('');

  const sendInvitationMutation = useSendInvitation();

  // Create a wrapper function for useEmployees to match EmployeeEmailSelect expectations
  const useEmployeesWrapper = (params: { page: number; limit: number; search: string }) => {
    const filters: EmployeesFilters = {
      page: params.page,
      limit: params.limit,
      search: params.search,
      status: 'ACTIVE', // Only show active employees
    };

    const result = useEmployees(filters);

    // Transform the response to match EmployeeEmailSelect expectations
    return {
      data: result.data
        ? {
            data: result.data.data,
            meta: {
              hasMore: result.data.meta.next !== null,
              total: result.data.meta.total,
              lastPage: result.data.meta.lastPage,
              currentPage: result.data.meta.currentPage,
            },
          }
        : undefined,
      isLoading: result.isLoading,
      error: result.error,
    };
  };

  // Validate email format
  const validateEmail = (emailValue: string) => {
    if (!emailValue.trim()) {
      setEmailError('Email is required');
      return false;
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailValue)) {
      setEmailError('Please enter a valid email address');
      return false;
    }
    setEmailError('');
    return true;
  };

  // Check if email is valid format (for new user invitation)
  const isValidNewEmail = (emailValue: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailValue.trim());
  };

  // Map employee data to select option format
  const mapEmployeeToOption = (employee: FullEmployee): EmployeeEmailSelectOption => ({
    id: employee.id,
    label: employee.fullName,
    sublabel: employee.email,
    avatar: `/placeholder.svg?height=32&width=32&text=${employee.fullName
      .split(' ')
      .map((n: string) => n[0])
      .join('')}`,
  });

  // Handle value change from the unified component
  const handleValueChange = (value: string, employee?: FullEmployee) => {
    console.log('handleValueChange called:', {
      value,
      employee: employee ? employee.fullName : 'none',
    });
    setEmail(value);
    setSelectedEmployee(employee || null);
    setEmailError('');
  };

  // Handle email change from the unified component
  const handleEmailChange = (emailValue: string) => {
    console.log('handleEmailChange called:', emailValue);
    setEmail(emailValue);
    setEmailError('');
    // Don't automatically clear selectedEmployee here - let the component logic handle it
  };

  // Handle send invitation (unified for both existing employees and new emails)
  const handleSendInvitation = async () => {
    if (!validateEmail(email)) return;

    try {
      await sendInvitationMutation.mutateAsync({
        email: email.trim(),
      });

      // Success message based on whether it's an existing employee or new user
      const successMessage = selectedEmployee
        ? `Successfully sent invitation to ${selectedEmployee.fullName} (${email})`
        : `Successfully sent invitation to ${email}`;

      toast.success(successMessage);
      handleClose();
    } catch (error) {
      toast.error(error);
    }
  };

  const handleClose = () => {
    setEmail('');
    setSelectedEmployee(null);
    setEmailError('');
    onClose();
  };

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      setEmail('');
      setSelectedEmployee(null);
      setEmailError('');
    }
  }, [isOpen]);

  const isLoading = sendInvitationMutation.isPending;
  const isEmployeeSelected = selectedEmployee !== null;
  // Can submit if we have a valid email (regardless of whether it's existing employee or new user)
  const canSubmit = email.trim() && isValidNewEmail(email) && !emailError;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="text-primary h-5 w-5" />
            Add Sub-Admin
          </DialogTitle>
          <DialogDescription>
            Enter an email address to send sub-admin invitation. You can type directly or select
            from existing employees.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Unified Employee/Email Selection */}
          <div className="space-y-2">
            <Label>Employee or Email Address *</Label>
            <EmployeeEmailSelect<FullEmployee>
              value={email}
              onValueChange={handleValueChange}
              onEmailChange={handleEmailChange}
              placeholder="Enter email or search employees"
              error={emailError}
              useQuery={useEmployeesWrapper}
              mapToOption={mapEmployeeToOption}
              searchPlaceholder="Type to search employees..."
              itemsPerPage={10}
              icon={<Mail className="h-4 w-4" />}
              disabled={isLoading}
            />
          </div>

          {/* Invitation Preview */}
          {email && (
            <div className="rounded-md border bg-blue-50 p-3">
              <div className="flex items-center gap-2">
                <UserPlus className="h-4 w-4 text-blue-600" />
                <div>
                  {isEmployeeSelected && selectedEmployee ? (
                    <>
                      <div className="text-sm font-medium text-blue-900">
                        {selectedEmployee.fullName}
                      </div>
                      <div className="text-sm text-blue-700">{selectedEmployee.email}</div>
                      <div className="text-xs text-blue-600">
                        Existing Employee • {selectedEmployee.jobTitle}
                      </div>
                    </>
                  ) : (
                    <>
                      <div className="text-sm font-medium text-blue-900">Sub-Admin Invitation</div>
                      <div className="text-sm text-blue-700">{email}</div>
                      <div className="text-xs text-blue-600">
                        Will receive an invitation email to join as sub-admin
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4">
            <Button type="button" variant="outline" onClick={handleClose} disabled={isLoading}>
              Cancel
            </Button>

            {/* Single Invitation Button */}
            <Button
              onClick={handleSendInvitation}
              className="bg-primary hover:bg-primary/90"
              disabled={isLoading || !canSubmit}
            >
              {sendInvitationMutation.isPending ? 'Sending Invitation...' : 'Send Invitation'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
