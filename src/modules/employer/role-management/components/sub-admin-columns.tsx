'use client';

import { Badge } from '@/components/ui/badge';
import { TableColumn, TableActionItem } from '@/components/table-component/data-table';
import { SubAdmin } from '../api/useSubAdmins';
import { UserCheck, UserX } from 'lucide-react';

export const subAdminColumns: TableColumn<SubAdmin>[] = [
  {
    key: 'name',
    header: 'Name',
    render: (subAdmin: SubAdmin) => (
      <div className="flex items-center gap-3">
        {/* <Avatar className="h-10 w-10">
          <AvatarFallback className="bg-primary/10 text-primary font-medium">
            {`${subAdmin.firstName[0]}${subAdmin.lastName[0]}`.toUpperCase()}
          </AvatarFallback>
        </Avatar> */}
        <div>
          <div className="font-medium text-gray-900 capitalize">
            {subAdmin.lastName ? `${subAdmin.firstName} ${subAdmin.lastName}` : subAdmin.firstName}
          </div>
        </div>
      </div>
    ),
  },
  {
    key: 'email',
    header: 'Email',
    render: (subAdmin: SubAdmin) => <div className="text-sm text-gray-600">{subAdmin.email}</div>,
  },
  {
    key: 'status',
    header: 'Status',
    render: (subAdmin: SubAdmin) => (
      <Badge
        className={
          subAdmin.isActive
            ? 'bg-green-100 text-green-800 hover:bg-green-100'
            : 'bg-red-100 text-red-800 hover:bg-red-100'
        }
      >
        {subAdmin.isActive ? 'Active' : 'Suspended'}
      </Badge>
    ),
  },
];

export const getSubAdminActions = (
  onToggleStatus: (id: string, isActive: boolean) => void
): TableActionItem<SubAdmin>[] => [
  {
    label: 'Suspend',
    icon: <UserX className="h-4 w-4" />,
    onClick: (subAdmin: SubAdmin) => onToggleStatus(subAdmin.id, false),
    className: 'text-red-600',
    show: (subAdmin: SubAdmin) => subAdmin.isActive,
  },
  {
    label: 'Activate',
    icon: <UserCheck className="h-4 w-4" />,
    onClick: (subAdmin: SubAdmin) => onToggleStatus(subAdmin.id, true),
    className: 'text-green-600',
    show: (subAdmin: SubAdmin) => !subAdmin.isActive,
  },
];
