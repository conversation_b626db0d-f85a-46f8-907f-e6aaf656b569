import { apiClient } from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';

export interface SubAdmin {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  companyId: string;
  companyName: string;
  isActive: boolean;
  isVerified: boolean;
  roles: string[];
  isEmployee: boolean;
  createdAt: string;
  updatedAt: string;
  lastLoginAt: string | null;
}

export interface SubAdminsResponse {
  data: SubAdmin[];
  meta: {
    total: number;
    lastPage: number;
    currentPage: number;
    perPage: number;
  };
  suspendedSubAdminsCount: number;
  activeSubAdminsCount: number;
}

interface UseSubAdminsParams {
  page?: number;
  limit?: number;
}

const fetchSubAdmins = async ({
  page = 1,
  limit = 10,
}: UseSubAdminsParams): Promise<SubAdminsResponse> => {
  const response = await apiClient.get(`/employer/sub-admins?page=${page}&limit=${limit}`);
  return response as unknown as SubAdminsResponse;
};

export const useSubAdmins = ({ page = 1, limit = 10 }: UseSubAdminsParams = {}) => {
  return useQuery({
    queryKey: ['subAdmins', page, limit],
    queryFn: () => fetchSubAdmins({ page, limit }),
  });
};
