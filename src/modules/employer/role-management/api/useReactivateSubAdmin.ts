import { apiClient } from '@/lib/api-client';
import { useMutation, useQueryClient } from '@tanstack/react-query';

const reactivateSubAdminApi = async (id: string) => {
  const response = await apiClient.put(`/employer/sub-admins/${id}/reactivate`);
  return response;
};

export const useReactivateSubAdmin = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => reactivateSubAdminApi(id),
    onSuccess: () => {
      // Invalidate sub-admins query to refresh the list
      queryClient.invalidateQueries({ queryKey: ['subAdmins'] });
    },
  });
};
