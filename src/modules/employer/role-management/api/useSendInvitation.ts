import { apiClient } from '@/lib/api-client';
import { useMutation, useQueryClient } from '@tanstack/react-query';

interface SendInvitationRequest {
  email: string;
}

interface SendInvitationResponse {
  statusCode: number;
  message: string;
  data: {
    id: string;
    email: string;
    invitedAt: string;
    status: string;
  };
  timestamp: string;
  path: string;
  method: string;
}

const sendInvitation = async (data: SendInvitationRequest): Promise<SendInvitationResponse> => {
  const response = await apiClient.post('/employer/sub-admins/send-invitation', data);
  return response as unknown as SendInvitationResponse;
};

export const useSendInvitation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: sendInvitation,
    onSuccess: () => {
      // Invalidate and refetch role management queries
      queryClient.invalidateQueries({ queryKey: ['sub-admins'] });
      queryClient.invalidateQueries({ queryKey: ['sub-admin-invitations'] });
      queryClient.invalidateQueries({ queryKey: ['employees-list'] });
    },
  });
};
