import { apiClient } from '@/lib/api-client';
import { useMutation, useQueryClient } from '@tanstack/react-query';

interface SuspendSubAdminPayload {
  reason: string;
}

const suspendSubAdminApi = async (id: string, payload: SuspendSubAdminPayload) => {
  const response = await apiClient.put(`/employer/sub-admins/${id}/suspend`, payload);
  return response;
};

export const useSuspendSubAdmin = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, payload }: { id: string; payload: SuspendSubAdminPayload }) =>
      suspendSubAdminApi(id, payload),
    onSuccess: () => {
      // Invalidate sub-admins query to refresh the list
      queryClient.invalidateQueries({ queryKey: ['subAdmins'] });
    },
  });
};
