'use client';

import type React from 'react';

import { useState, useEffect } from 'react';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import { format } from 'date-fns';
import { Calendar } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useCreateEmployee } from '../api/useCreateEmployee';
import { useUpdateEmployee } from '../api/useUpdateEmployee';
import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import { Employee } from '../api/useEmployees';

const employeeSchema = z.object({
  fullName: z.string().min(1, 'Full name is required'),
  email: z.email('Please enter a valid email address'),
  phone: z.string().min(1, 'Phone number is required'),
  address: z.string().min(1, 'Address is required'),
  jobTitle: z.string().min(1, 'Job title is required'),
  startDate: z.date({ message: 'Start date is required' }),
});

type EmployeeFormData = z.infer<typeof employeeSchema>;

interface CreateUpdateEmployeeModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: 'create' | 'edit';
  employeeData?: Employee;
}

// Utility function to convert ISO date string to Date object
const parseISODateToDate = (isoDate: string): Date => {
  return new Date(isoDate.split('T')[0] + 'T00:00:00.000Z');
};

export const CreateUpdateEmployeeModal = ({
  isOpen,
  onClose,
  mode,
  employeeData,
}: CreateUpdateEmployeeModalProps) => {
  const queryClient = useQueryClient();
  const [isLoading, setIsLoading] = useState(false);
  const { mutateAsync: createEmployee } = useCreateEmployee();
  const { mutateAsync: updateEmployee } = useUpdateEmployee();

  const form = useForm<EmployeeFormData>({
    resolver: zodResolver(employeeSchema),
    defaultValues: {
      fullName: '',
      email: '',
      phone: '',
      address: '',
      jobTitle: '',
      startDate: undefined,
    },
  });

  // Effect to populate form when editing
  useEffect(() => {
    if (mode === 'edit' && employeeData) {
      form.reset({
        fullName: employeeData.fullName,
        email: employeeData.email,
        // Remove '+' prefix when populating the form for editing
        phone: employeeData.phone?.startsWith('+')
          ? employeeData.phone.slice(1)
          : employeeData.phone,
        address: employeeData.address,
        jobTitle: employeeData.jobTitle,
        startDate: parseISODateToDate(employeeData.startDate),
      });
    } else if (mode === 'create') {
      form.reset({
        fullName: '',
        email: '',
        phone: '',
        address: '',
        jobTitle: '',
        startDate: undefined,
      });
    }
  }, [mode, employeeData, form]);

  const handleSubmit = async (data: EmployeeFormData) => {
    setIsLoading(true);

    try {
      // Ensure phone number has '+' prefix for submission and format date
      const formattedData = {
        ...data,
        phone: data.phone.startsWith('+') ? data.phone : `+${data.phone}`,
        startDate: format(data.startDate, 'yyyy-MM-dd'),
      };

      if (mode === 'create') {
        await createEmployee(formattedData);
        toast.success('Employee has been created successfully!');
      } else {
        await updateEmployee({ id: employeeData!.id, ...formattedData });
        toast.success('Employee has been updated successfully!');
      }

      form.reset();
      queryClient.invalidateQueries({ queryKey: ['employees-list'] });

      // If editing, also invalidate the specific employee details
      if (mode === 'edit' && employeeData) {
        queryClient.invalidateQueries({ queryKey: ['employee-details', employeeData.id] });
      }

      onClose();
    } catch (error) {
      toast.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    form.reset();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{mode === 'create' ? 'Add New Employee' : 'Edit Employee'}</DialogTitle>
          <DialogDescription>
            {mode === 'create'
              ? 'Create a new employee account with their basic information.'
              : 'Update the employee information below.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="fullName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Name</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter full name" disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="email"
                        placeholder="Enter email address"
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone</FormLabel>
                    <FormControl>
                      <PhoneInput
                        country={'us'}
                        value={field.value}
                        onChange={(phone) => field.onChange(phone)}
                        disabled={isLoading}
                        inputStyle={{
                          width: '100%',
                          height: '35px',
                          fontSize: '14px',
                          border: '1px solid #e2e8f0',
                          borderRadius: '6px',
                          paddingLeft: '48px',
                          boxShadow: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
                        }}
                        containerStyle={{
                          width: '100%',
                        }}
                        buttonStyle={{
                          border: '1px solid #e2e8f0',
                          borderRadius: '6px 0 0 6px',
                          backgroundColor: '#f8fafc',
                          boxShadow: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
                        }}
                        dropdownStyle={{
                          borderRadius: '6px',
                          border: '1px solid #e2e8f0',
                          boxShadow: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="startDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Start Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              'w-full justify-start pl-3 text-left font-normal',
                              !field.value && 'text-muted-foreground'
                            )}
                            disabled={isLoading}
                          >
                            {field.value ? (
                              format(field.value, 'yyyy-MM-dd')
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <Calendar className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <CalendarComponent
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="jobTitle"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Job Title</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Enter job title" disabled={isLoading} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Enter full address"
                      rows={3}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-2 pt-4">
              <Button type="button" variant="outline" onClick={handleClose} disabled={isLoading}>
                Cancel
              </Button>
              <Button
                type="submit"
                className="bg-[#F74464] hover:bg-[#F74464]/90"
                disabled={isLoading}
              >
                {isLoading
                  ? mode === 'create'
                    ? 'Creating...'
                    : 'Updating...'
                  : mode === 'create'
                    ? 'Create Employee'
                    : 'Update Employee'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
