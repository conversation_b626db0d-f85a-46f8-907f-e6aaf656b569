'use client';

import { useEffect, useState } from 'react';
import { useSearch<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Shield, CheckCircle, XCircle, Loader2 } from 'lucide-react';
import { useVerifyEmployeeInvitation } from '../api/useVerifyEmployeeInvitation';
import { useAcceptSubAdminInvitation } from '../api/useAcceptSubAdminInvitation';
import { toast } from '@/lib/toast';
import AuthLeftLayout from '@/modules/auth/layout/auth-left-layout';
import Image from 'next/image';

const subAdminInvitationSchema = z.object({
  firstName: z
    .string()
    .min(1, 'First name is required')
    .min(2, 'First name must be at least 2 characters'),
  lastName: z
    .string()
    .min(1, 'Last name is required')
    .min(2, 'Last name must be at least 2 characters'),
  phoneNumber: z.string().min(1, 'Phone number is required'),
  password: z
    .string()
    .min(1, 'Password is required')
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number')
    .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character'),
});

type SubAdminInvitationFormData = z.infer<typeof subAdminInvitationSchema>;

export const SubAdminInvitationModule = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [token, setToken] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isAlreadyAccepted, setIsAlreadyAccepted] = useState(false);

  const verifyQuery = useVerifyEmployeeInvitation(token);
  const acceptMutation = useAcceptSubAdminInvitation();

  const form = useForm<SubAdminInvitationFormData>({
    resolver: zodResolver(subAdminInvitationSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      phoneNumber: '',
      password: '',
    },
  });

  useEffect(() => {
    const tokenParam = searchParams.get('token');
    setToken(tokenParam);
    setIsInitialized(true);
  }, [searchParams]);

  const handleSubmit = async (data: SubAdminInvitationFormData) => {
    if (!token) return;

    setIsSubmitting(true);
    try {
      const payload = {
        ...data,
        phoneNumber: data.phoneNumber.startsWith('+') ? data.phoneNumber : `+${data.phoneNumber}`,
      };

      const response = await acceptMutation.mutateAsync({ token, payload });

      // Check if invitation is already accepted
      if (response?.message === 'Invitation is accepted') {
        setIsAlreadyAccepted(true);
        setIsSubmitting(false);
        return;
      }

      toast.success('Account created successfully! Please login to continue.');
      router.push('/');
    } catch (error: any) {
      if (
        error?.message === 'Invitation is accepted' ||
        error?.response?.data?.message === 'Invitation is accepted'
      ) {
        setIsAlreadyAccepted(true);
      } else {
        toast.error(error);
      }
      setIsSubmitting(false);
    }
  };

  const renderContent = () => {
    // Show loading while initializing/checking for token
    if (!isInitialized) {
      return (
        <Card className="mx-auto w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mb-4 flex justify-center">
              <Loader2 className="h-16 w-16 animate-spin text-blue-500" />
            </div>
            <CardTitle>Loading</CardTitle>
            <CardDescription>Please wait while we process your invitation...</CardDescription>
          </CardHeader>
        </Card>
      );
    }

    // No token in URL
    if (!token) {
      return (
        <Card className="mx-auto w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mb-4 flex justify-center">
              <XCircle className="h-16 w-16 text-red-500" />
            </div>
            <CardTitle className="text-red-600">Invalid Invitation</CardTitle>
            <CardDescription>
              The invitation link is invalid or missing required parameters.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <p className="mb-4 text-sm text-gray-600">
              Please check your email for the correct invitation link or contact your administrator.
            </p>
            <Button onClick={() => router.push('/')} variant="outline" className="w-full">
              Go to Login
            </Button>
          </CardContent>
        </Card>
      );
    }

    // Loading verification
    if (verifyQuery.isLoading) {
      return (
        <Card className="mx-auto w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mb-4 flex justify-center">
              <Loader2 className="h-16 w-16 animate-spin text-blue-500" />
            </div>
            <CardTitle>Verifying Invitation</CardTitle>
            <CardDescription>Please wait while we verify your invitation...</CardDescription>
          </CardHeader>
        </Card>
      );
    }

    // Verification failed
    if (verifyQuery.isError || !verifyQuery.data) {
      return (
        <Card className="mx-auto w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mb-4 flex justify-center">
              <XCircle className="h-16 w-16 text-red-500" />
            </div>
            <CardTitle className="text-red-600">Invalid or Expired Token</CardTitle>
            <CardDescription>
              This invitation token has expired or is no longer valid.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <p className="mb-4 text-sm text-gray-600">
              Please request a new invitation from your employer or contact support for assistance.
            </p>
          </CardContent>
        </Card>
      );
    }

    // Valid token - show form or already accepted message
    return (
      <Card className="mx-auto w-full max-w-lg">
        <CardHeader className="pb-4 text-center">
          <div className="mb-4 flex justify-center">
            {isAlreadyAccepted ? (
              <CheckCircle className="h-16 w-16 text-green-500" />
            ) : (
              <Shield className="text-primary h-16 w-16" />
            )}
          </div>
          <CardTitle className={isAlreadyAccepted ? 'text-green-600' : 'text-primary'}>
            {isAlreadyAccepted ? 'Already a Member' : 'Sub-Admin Invitation'}
          </CardTitle>
          <CardDescription>
            {isAlreadyAccepted
              ? 'You are already a Sub-Administrator'
              : 'Complete your registration to become a Sub-Administrator'}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {isAlreadyAccepted ? (
            <div className="space-y-4 text-center">
              <p className="text-sm text-gray-700">
                <strong>You&apos;re all set!</strong> You have already accepted this invitation and
                are now a Sub-Administrator for your organization.
              </p>
              <p className="text-xs text-gray-600">
                You can now access the admin panel to manage employees, view analytics, and perform
                administrative tasks.
              </p>
              <div className="border-t pt-4">
                <Button
                  onClick={() => router.push('/')}
                  className="w-full bg-green-600 hover:bg-green-700"
                >
                  Go to Login
                </Button>
              </div>
            </div>
          ) : (
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="firstName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>First Name</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="Enter first name"
                            disabled={isSubmitting}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="lastName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Last Name</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Enter last name" disabled={isSubmitting} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="phoneNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number</FormLabel>
                      <FormControl>
                        <PhoneInput
                          country={'us'}
                          value={field.value}
                          onChange={(phone) => field.onChange(phone)}
                          disabled={isSubmitting}
                          inputStyle={{
                            width: '100%',
                            height: '35px',
                            fontSize: '14px',
                            border: '1px solid #e2e8f0',
                            borderRadius: '6px',
                            paddingLeft: '48px',
                            boxShadow: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
                          }}
                          containerStyle={{
                            width: '100%',
                          }}
                          buttonStyle={{
                            border: '1px solid #e2e8f0',
                            borderRadius: '6px 0 0 6px',
                            backgroundColor: '#f8fafc',
                            boxShadow: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="password"
                          placeholder="Enter a secure password"
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="border-t pt-4">
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="bg-primary hover:bg-primary/80 w-full"
                  >
                    {isSubmitting ? (
                      <div className="flex items-center space-x-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span>Submitting...</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4" />
                        <span>Submit invitation</span>
                      </div>
                    )}
                  </Button>
                </div>
              </form>
            </Form>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="flex min-h-screen">
      {/* Left Side - Branding (70%) */}
      <AuthLeftLayout />

      {/* Right Side - Invitation Form (30%) */}
      <div className="flex w-3/6 items-center justify-center bg-white p-8">
        <div className="w-full max-w-lg space-y-8">
          {/* Header */}
          <div className="text-center">
            <div className="flex justify-center pb-3">
              <Image
                src="/logo_finwage.svg"
                alt="FinWage Logo"
                width={120}
                height={60}
                className="h-16 w-auto"
                priority
              />
            </div>
            <h2 className="text-3xl font-bold text-gray-900">Sub-Admin Invitation</h2>
            <p className="mt-2 text-gray-600">Create your account to get started</p>
          </div>

          {/* Content */}
          {renderContent()}
        </div>
      </div>
    </div>
  );
};
