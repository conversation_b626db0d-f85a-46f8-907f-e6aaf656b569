'use client';

import { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Shield, CheckCircle, XCircle, Loader2 } from 'lucide-react';
import { useVerifyEmployeeInvitation } from '../api/useVerifyEmployeeInvitation';
import { useAcceptEmployeeInvitation } from '../api/useAcceptEmployeeInvitation';
import { toast } from '@/lib/toast';
import AuthLeftLayout from '@/modules/auth/layout/auth-left-layout';
import Image from 'next/image';

export function SubAdminEmployeeInvitationPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [token, setToken] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isAccepting, setIsAccepting] = useState(false);
  const [isAlreadyAccepted, setIsAlreadyAccepted] = useState(false);

  const verifyQuery = useVerifyEmployeeInvitation(token);
  const acceptMutation = useAcceptEmployeeInvitation();

  useEffect(() => {
    const tokenParam = searchParams.get('token');
    setToken(tokenParam);
    setIsInitialized(true);
  }, [searchParams]);

  const handleAcceptInvitation = async () => {
    if (!token) return;

    setIsAccepting(true);
    try {
      const response = await acceptMutation.mutateAsync(token);

      // Check if invitation is already accepted
      if (response?.message === 'Invitation is accepted') {
        setIsAlreadyAccepted(true);
        setIsAccepting(false);
        return;
      }

      toast.success('Invitation accepted successfully! Please login to continue.');
      router.push('/');
    } catch (error: any) {
      // Check if the error message indicates already accepted
      if (
        error?.message === 'Invitation is accepted' ||
        error?.response?.data?.message === 'Invitation is accepted'
      ) {
        setIsAlreadyAccepted(true);
      } else {
        toast.error(error);
      }
      setIsAccepting(false);
    }
  };

  const renderContent = () => {
    // Show loading while initializing/checking for token
    if (!isInitialized) {
      return (
        <Card className="mx-auto w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mb-4 flex justify-center">
              <Loader2 className="h-16 w-16 animate-spin text-blue-500" />
            </div>
            <CardTitle>Loading</CardTitle>
            <CardDescription>Please wait while we process your invitation...</CardDescription>
          </CardHeader>
        </Card>
      );
    }

    // No token in URL
    if (!token) {
      return (
        <Card className="mx-auto w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mb-4 flex justify-center">
              <XCircle className="h-16 w-16 text-red-500" />
            </div>
            <CardTitle className="text-red-600">Invalid Invitation</CardTitle>
            <CardDescription>
              The invitation link is invalid or missing required parameters.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <p className="mb-4 text-sm text-gray-600">
              Please check your email for the correct invitation link or contact your administrator.
            </p>
            {/* <Button onClick={() => router.push('/')} variant="outline" className="w-full">
              Go to Login
            </Button> */}
          </CardContent>
        </Card>
      );
    }

    // Loading verification
    if (verifyQuery.isLoading) {
      return (
        <Card className="mx-auto w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mb-4 flex justify-center">
              <Loader2 className="h-16 w-16 animate-spin text-blue-500" />
            </div>
            <CardTitle>Verifying Invitation</CardTitle>
            <CardDescription>Please wait while we verify your invitation...</CardDescription>
          </CardHeader>
        </Card>
      );
    }

    // Verification failed
    if (verifyQuery.isError || !verifyQuery.data) {
      return (
        <Card className="mx-auto w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mb-4 flex justify-center">
              <XCircle className="h-16 w-16 text-red-500" />
            </div>
            <CardTitle className="text-red-600">Invalid or Expired Token</CardTitle>
            <CardDescription>
              This invitation token has expired or is no longer valid.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <p className="mb-4 text-sm text-gray-600">
              Please request a new invitation from your employer or contact support for assistance.
            </p>
          </CardContent>
        </Card>
      );
    }

    // Valid token - show acceptance form or already accepted message
    return (
      <Card className="mx-auto w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mb-4 flex justify-center">
            {isAlreadyAccepted ? (
              <CheckCircle className="h-16 w-16 text-green-500" />
            ) : (
              <Shield className="text-primary h-16 w-16" />
            )}
          </div>
          <CardTitle className={isAlreadyAccepted ? 'text-green-600' : 'text-primary'}>
            {isAlreadyAccepted ? 'Already a Member' : 'Sub-Admin Invitation'}
          </CardTitle>
          <CardDescription>
            {isAlreadyAccepted
              ? 'You are already a Sub-Administrator'
              : "You've been invited to become a Sub-Administrator"}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {isAlreadyAccepted ? (
            <div className="space-y-4 text-center">
              <p className="text-sm text-gray-700">
                <strong>You&apos;re all set!</strong> You have already accepted this invitation and
                are now a Sub-Administrator for your organization.
              </p>
              <p className="text-xs text-gray-600">
                You can now access the admin panel to manage employees, view analytics, and perform
                administrative tasks.
              </p>
              <div className="border-t pt-4">
                <Button
                  onClick={() => router.push('/')}
                  className="w-full bg-green-600 hover:bg-green-700"
                >
                  Go to Login
                </Button>
              </div>
            </div>
          ) : (
            <>
              <div className="space-y-2 text-center">
                <p className="text-sm text-gray-700">
                  <strong>Congratulations!</strong> You have been invited to become a
                  Sub-Administrator for your organization.
                </p>
                <p className="text-xs text-gray-600">
                  As a Sub-Admin, you&apos;ll have access to manage employees, view analytics, and
                  assist with administrative tasks.
                </p>
              </div>

              <div className="border-t pt-4">
                <Button
                  onClick={handleAcceptInvitation}
                  disabled={acceptMutation.isPending || isAccepting}
                  className="bg-primary hover:bg-primary/80 w-full"
                >
                  {acceptMutation.isPending || isAccepting ? (
                    <div className="flex items-center space-x-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span>Accepting Invitation...</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4" />
                      <span>Accept Invitation</span>
                    </div>
                  )}
                </Button>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="flex min-h-screen">
      {/* Left Side - Branding (70%) */}
      <AuthLeftLayout />

      {/* Right Side - Invitation Form (30%) */}
      <div className="flex w-3/6 items-center justify-center bg-white p-8">
        <div className="w-full max-w-md space-y-8">
          {/* Header */}
          <div className="text-center">
            <div className="flex justify-center pb-3">
              <Image
                src="/logo_finwage.svg"
                alt="FinWage Logo"
                width={120}
                height={60}
                className="h-16 w-auto"
                priority
              />
            </div>
            <h2 className="text-3xl font-bold text-gray-900">Sub-Admin Invitation</h2>
            <p className="mt-2 text-gray-600">Accept your role as Sub-Administrator</p>
          </div>

          {/* Content */}
          {renderContent()}
        </div>
      </div>
    </div>
  );
}
