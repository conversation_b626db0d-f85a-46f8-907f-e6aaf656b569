import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';

const acceptEmployeeInvitationApi = async (token: string) => {
  const response = await apiClient.post(`/sub-admin-invitations/accept-employee/${token}`);
  return response.data;
};

export const useAcceptEmployeeInvitation = () => {
  return useMutation({
    mutationFn: acceptEmployeeInvitationApi,
  });
};
