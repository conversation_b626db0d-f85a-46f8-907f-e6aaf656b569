import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';

interface AcceptSubAdminInvitationPayload {
  firstName: string;
  lastName: string;
  phoneNumber: string;
  password: string;
}

const acceptSubAdminInvitationApi = async (
  token: string,
  payload: AcceptSubAdminInvitationPayload
) => {
  const response = await apiClient.post(`/sub-admin-invitations/accept/${token}`, payload);
  return response.data;
};

export const useAcceptSubAdminInvitation = () => {
  return useMutation({
    mutationFn: ({ token, payload }: { token: string; payload: AcceptSubAdminInvitationPayload }) =>
      acceptSubAdminInvitationApi(token, payload),
  });
};
