import { apiClient } from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';

const verifyEmployeeInvitationApi = async (token: string) => {
  const response = await apiClient.get(`/sub-admin-invitations/verify/${token}`);
  return response.data;
};

export const useVerifyEmployeeInvitation = (token: string | null) => {
  return useQuery({
    queryKey: ['verify-employee-invitation', token],
    queryFn: () => verifyEmployeeInvitationApi(token!),
    enabled: !!token,
    retry: false,
  });
};
