'use client';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Save, DollarSign, Loader2 } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { toast } from '@/lib/toast';
import { useGetFinwageCommissionSettings } from '../api/useGetFinwageCommissionSettings';
import { useUpdateFinwageCommissionSettings } from '../api/useUpdateFinwageCommissionSettings';

// Zod schema for form validation
const finwageCommissionSchema = z.object({
  value: z
    .string()
    .min(1, 'Commission amount is required')
    .refine((val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num >= 0.01 && num <= 100;
    }, 'Please enter a valid commission between 0.01 and 100'),
});

type FinwageCommissionFormData = z.infer<typeof finwageCommissionSchema>;

export function FinwageCommissionCard() {
  const form = useForm<FinwageCommissionFormData>({
    resolver: zodResolver(finwageCommissionSchema),
    defaultValues: { value: '' },
  });

  const {
    data: finwageData,
    isLoading: isLoadingFinwage,
    error: finwageError,
  } = useGetFinwageCommissionSettings();
  const updateFinwageMutation = useUpdateFinwageCommissionSettings();

  // Effect to populate form with fetched data
  useEffect(() => {
    if (finwageData?.data?.config?.value) {
      form.reset({ value: finwageData.data.config.value.toString() });
    }
  }, [finwageData, form]);

  // Submit handler
  const onSubmit = async (data: FinwageCommissionFormData) => {
    try {
      await updateFinwageMutation.mutateAsync({ type: 'FIXED', value: parseFloat(data.value) });
      toast.success('FinWage commission updated successfully');
    } catch (error: any) {
      toast.error(error?.message || 'Failed to update FinWage commission. Please try again.');
    }
  };

  if (finwageError) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-red-600">
            <p>Failed to load FinWage commission settings.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-5">
        <CardTitle className="flex items-center gap-2">
          <DollarSign className="h-5 w-5 text-green-600" />
          FinWage Commission
        </CardTitle>
        <CardDescription>
          Set the global commission percentage that FinWage charges on transactions
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoadingFinwage ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="text-muted-foreground ml-2 text-sm">Loading...</span>
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="value"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel htmlFor="finwage-commission">Commission (%)</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          {...field}
                          id="finwage-commission"
                          type="number"
                          min="0.01"
                          max="100"
                          step="0.01"
                          placeholder="Enter commission"
                          className="pr-8"
                          disabled={updateFinwageMutation.isPending}
                        />
                        <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                          <span className="text-sm text-gray-400">%</span>
                        </div>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="pt-2">
                <Button
                  type="submit"
                  disabled={
                    updateFinwageMutation.isPending ||
                    !form.formState.isDirty ||
                    !form.formState.isValid
                  }
                  className="w-full"
                >
                  {updateFinwageMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating Commission...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Update FinWage Commission
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        )}
      </CardContent>
    </Card>
  );
}
