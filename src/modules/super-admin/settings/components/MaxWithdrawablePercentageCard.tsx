'use client';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Save, Percent, Loader2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { toast } from '@/lib/toast';
import { useGetMaxWithdrawableSettings } from '../api/useGetMaxWithdrawableSettings';
import { useUpdateMaxWithdrawableSettings } from '../api/useUpdateMaxWithdrawableSettings';

// Zod schema for form validation
const withdrawablePercentageSchema = z.object({
  percentage: z
    .string()
    .min(1, 'Percentage is required')
    .refine((val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num >= 0.01 && num <= 100;
    }, 'Please enter a valid percentage between 0.01 and 100'),
});

type WithdrawablePercentageFormData = z.infer<typeof withdrawablePercentageSchema>;

export function MaxWithdrawablePercentageCard() {
  const form = useForm<WithdrawablePercentageFormData>({
    resolver: zodResolver(withdrawablePercentageSchema),
    defaultValues: { percentage: '' },
  });

  const {
    data: withdrawableData,
    isLoading: isLoadingWithdrawable,
    error: withdrawableError,
  } = useGetMaxWithdrawableSettings();
  const updateWithdrawableMutation = useUpdateMaxWithdrawableSettings();

  // Effect to populate form with fetched data
  useEffect(() => {
    if (withdrawableData?.data?.config?.percentage) {
      form.reset({ percentage: withdrawableData.data.config.percentage.toString() });
    }
  }, [withdrawableData, form]);

  // Submit handler
  const onSubmit = async (data: WithdrawablePercentageFormData) => {
    try {
      await updateWithdrawableMutation.mutateAsync({ percentage: parseFloat(data.percentage) });
      toast.success('Maximum withdrawable percentage updated successfully');
    } catch (error: any) {
      toast.error(error?.message || 'Failed to update withdrawable percentage. Please try again.');
    }
  };

  if (withdrawableError) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-red-600">
            <p>Failed to load withdrawable percentage settings.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-5">
        <CardTitle className="flex items-center gap-2">
          <Percent className="h-5 w-5 text-blue-600" />
          Maximum Withdrawable Percentage
        </CardTitle>
        <CardDescription>
          Set the maximum percentage of earned salary that employees can request as advance
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoadingWithdrawable ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="text-muted-foreground ml-2 text-sm">Loading...</span>
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="percentage"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel htmlFor="withdrawable-percentage">Percentage (%)</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          {...field}
                          id="withdrawable-percentage"
                          type="number"
                          min="0.01"
                          max="100"
                          step="0.01"
                          placeholder="Enter percentage"
                          className="pr-8"
                          disabled={updateWithdrawableMutation.isPending}
                        />
                        <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                          <span className="text-sm text-gray-400">%</span>
                        </div>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="pt-2">
                <Button
                  type="submit"
                  disabled={
                    updateWithdrawableMutation.isPending ||
                    !form.formState.isDirty ||
                    !form.formState.isValid
                  }
                  className="w-full"
                >
                  {updateWithdrawableMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating Percentage...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Update Withdrawable Limit
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        )}
      </CardContent>
    </Card>
  );
}
