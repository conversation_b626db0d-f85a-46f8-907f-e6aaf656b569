'use client';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Save, Building, Loader2, Calendar } from 'lucide-react';
import { format } from 'date-fns';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { toast } from '@/lib/toast';
import { useGetEmployerCommissionSettings } from '../api/useGetEmployerCommissionSettings';
import { useUpdateEmployerCommissionSettings } from '../api/useUpdateEmployerCommissionSettings';

// Zod schema for form validation
const employerCommissionSchema = z.object({
  value: z
    .string()
    .min(1, 'Commission percentage is required')
    .refine((val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num >= 0.01 && num <= 100;
    }, 'Please enter a valid percentage between 0.01 and 100'),
  effectiveFrom: z.date({ message: 'Effective date is required' }),
});

type EmployerCommissionFormData = z.infer<typeof employerCommissionSchema>;

export function EmployerCommissionCard() {
  const form = useForm<EmployerCommissionFormData>({
    resolver: zodResolver(employerCommissionSchema),
    defaultValues: { value: '', effectiveFrom: undefined },
  });

  const {
    data: employerData,
    isLoading: isLoadingEmployer,
    error: employerError,
  } = useGetEmployerCommissionSettings();
  const updateEmployerMutation = useUpdateEmployerCommissionSettings();

  // Effect to populate form with fetched data
  useEffect(() => {
    if (employerData?.data) {
      const effectiveDate = employerData.data.effectiveFrom
        ? new Date(employerData.data.effectiveFrom)
        : undefined;
      form.reset({
        value: employerData.data.value.toString(),
        effectiveFrom: effectiveDate,
      });
    }
  }, [employerData, form]);

  // Submit handler
  const onSubmit = async (data: EmployerCommissionFormData) => {
    try {
      if (!employerData?.data?.id) {
        toast.error('Commission setting ID not found. Please refresh and try again.');
        return;
      }
      await updateEmployerMutation.mutateAsync({
        commissionSettingId: employerData.data.id,
        commissionType: 'FIXED',
        value: parseFloat(data.value),
        effectiveFrom: data.effectiveFrom.toISOString(),
      });
      toast.success('Employer commission updated successfully');
    } catch (error: any) {
      toast.error(error?.message || 'Failed to update employer commission. Please try again.');
    }
  };

  if (employerError) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-red-600">
            <p>Failed to load employer commission settings.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-5">
        <CardTitle className="flex items-center gap-2">
          <Building className="h-5 w-5 text-purple-600" />
          Employer Commission
        </CardTitle>
        <CardDescription>
          Set the global commission percentage that employers pay for salary advance services
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoadingEmployer ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="text-muted-foreground ml-2 text-sm">Loading...</span>
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="value"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel htmlFor="employer-commission">Commission (%)</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          {...field}
                          id="employer-commission"
                          type="number"
                          min="0.01"
                          max="100"
                          step="0.01"
                          placeholder="Enter commission"
                          className="pr-8"
                          disabled={updateEmployerMutation.isPending}
                        />
                        <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                          <span className="text-sm text-gray-400">%</span>
                        </div>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="effectiveFrom"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel htmlFor="effective-date">Effective From</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              'w-full justify-start pl-3 text-left font-normal',
                              !field.value && 'text-muted-foreground'
                            )}
                            disabled={updateEmployerMutation.isPending}
                          >
                            {field.value ? (
                              format(field.value, 'yyyy/MM/dd')
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <Calendar className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <CalendarComponent
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date: Date) => {
                            const today = new Date();
                            today.setHours(0, 0, 0, 0); // Set to start of today
                            const compareDate = new Date(date);
                            compareDate.setHours(0, 0, 0, 0); // Set to start of selected date
                            return compareDate < today;
                          }}
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="pt-2">
                <Button
                  type="submit"
                  disabled={
                    updateEmployerMutation.isPending ||
                    !form.formState.isDirty ||
                    !form.formState.isValid
                  }
                  className="w-full"
                >
                  {updateEmployerMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating Commission...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Update Employer Commission
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        )}
      </CardContent>
    </Card>
  );
}
