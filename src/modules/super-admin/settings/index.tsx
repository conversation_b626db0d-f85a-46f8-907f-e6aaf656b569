import React from 'react';
import Header from '@/components/reusable-component/header';
import { MaxWithdrawablePercentageCard } from './components/MaxWithdrawablePercentageCard';
import { FinwageCommissionCard } from './components/FinwageCommissionCard';
import { EmployerCommissionCard } from './components/EmployerCommissionCard';

const SuperAdminSettingsModule = () => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <Header
        title="Settings"
        description="Configure system-wide commission settings and salary advance limits"
      />

      {/* Two Column Grid Layout */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <MaxWithdrawablePercentageCard />
        <FinwageCommissionCard />

        <EmployerCommissionCard />
      </div>
    </div>
  );
};

export default SuperAdminSettingsModule;
