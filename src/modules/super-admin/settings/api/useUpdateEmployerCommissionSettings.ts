import { apiClient } from '@/lib/api-client';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { EMPLOYER_COMMISSION_KEYS } from './useGetEmployerCommissionSettings';

export interface UpdateEmployerCommissionSettingsPayload {
  commissionType: string;
  value: number;
  effectiveFrom: string;
}

export interface UpdateEmployerCommissionSettingsData {
  id: string;
  type: string;
  companyId: string | null;
  commissionType: string;
  value: number;
  isActive: boolean;
  effectiveFrom: string;
  effectiveTo: string | null;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

export interface UpdateEmployerCommissionSettingsResponse {
  statusCode: number;
  message: string;
  data: UpdateEmployerCommissionSettingsData;
  timestamp: string;
  path: string;
  method: string;
}

// Update employer commission settings
const updateEmployerCommissionSettings = async (
  commissionSettingId: string,
  payload: UpdateEmployerCommissionSettingsPayload
): Promise<UpdateEmployerCommissionSettingsResponse> => {
  const response = await apiClient.put(
    `/admin/settings/commission/${commissionSettingId}`,
    payload
  );

  return response as unknown as UpdateEmployerCommissionSettingsResponse;
};

export const useUpdateEmployerCommissionSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      commissionSettingId,
      ...payload
    }: UpdateEmployerCommissionSettingsPayload & { commissionSettingId: string }) =>
      updateEmployerCommissionSettings(commissionSettingId, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: EMPLOYER_COMMISSION_KEYS.global(),
      });
    },
  });
};
