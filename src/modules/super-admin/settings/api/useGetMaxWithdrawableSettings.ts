import { apiClient } from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';

export interface MaxWithdrawableSettingsData {
  id: string;
  key: string;
  config: {
    percentage: number;
  };
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

export interface MaxWithdrawableSettingsResponse {
  statusCode: number;
  message: string;
  data: MaxWithdrawableSettingsData;
  timestamp: string;
  path: string;
  method: string;
}

const SETTINGS_KEYS = {
  all: ['admin-settings'] as const,
  maxWithdrawable: () => [...SETTINGS_KEYS.all, 'max-withdrawable-percentage'] as const,
};

// Fetch max withdrawable percentage settings
const fetchMaxWithdrawableSettings = async (): Promise<MaxWithdrawableSettingsResponse> => {
  const response = await apiClient.get('/admin/settings/max-withdrawable-percentage');

  // The API client's response interceptor returns response.data,
  // so response is already the API response body
  return response as unknown as MaxWithdrawableSettingsResponse;
};

export const useGetMaxWithdrawableSettings = () => {
  return useQuery({
    queryKey: SETTINGS_KEYS.maxWithdrawable(),
    queryFn: fetchMaxWithdrawableSettings,
  });
};

export { SETTINGS_KEYS };
