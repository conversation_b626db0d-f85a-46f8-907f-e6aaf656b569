import { apiClient } from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';

export interface FinwageCommissionSettingsData {
  id: string;
  key: string;
  config: {
    type: string;
    value: number;
    minAmount: number | null;
    maxAmount: number | null;
  };
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

export interface FinwageCommissionSettingsResponse {
  statusCode: number;
  message: string;
  data: FinwageCommissionSettingsData;
  timestamp: string;
  path: string;
  method: string;
}

const FINWAGE_COMMISSION_KEYS = {
  all: ['admin-settings', 'finwage-commission'] as const,
  transactionFee: () => [...FINWAGE_COMMISSION_KEYS.all, 'transaction-fee'] as const,
};

// Fetch FinWage transaction fee settings
const fetchFinwageCommissionSettings = async (): Promise<FinwageCommissionSettingsResponse> => {
  const response = await apiClient.get('/admin/settings/transaction-fee');

  return response as unknown as FinwageCommissionSettingsResponse;
};

export const useGetFinwageCommissionSettings = () => {
  return useQuery({
    queryKey: FINWAGE_COMMISSION_KEYS.transactionFee(),
    queryFn: fetchFinwageCommissionSettings,
  });
};

export { FINWAGE_COMMISSION_KEYS };
