import { apiClient } from '@/lib/api-client';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { SETTINGS_KEYS } from './useGetMaxWithdrawableSettings';

export interface UpdateMaxWithdrawableSettingsPayload {
  percentage: number;
}

export interface UpdateMaxWithdrawableSettingsData {
  id: string;
  key: string;
  config: {
    percentage: number;
  };
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

export interface UpdateMaxWithdrawableSettingsResponse {
  statusCode: number;
  message: string;
  data: UpdateMaxWithdrawableSettingsData;
  timestamp: string;
  path: string;
  method: string;
}

// Update max withdrawable percentage settings
const updateMaxWithdrawableSettings = async (
  payload: UpdateMaxWithdrawableSettingsPayload
): Promise<UpdateMaxWithdrawableSettingsResponse> => {
  const response = await apiClient.put('/admin/settings/max-withdrawable-percentage', payload);

  return response as unknown as UpdateMaxWithdrawableSettingsResponse;
};

export const useUpdateMaxWithdrawableSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateMaxWithdrawableSettings,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: SETTINGS_KEYS.maxWithdrawable(),
      });
    },
  });
};
