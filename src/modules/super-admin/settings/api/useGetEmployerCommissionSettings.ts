import { apiClient } from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';

export interface EmployerCommissionSettingsData {
  id: string;
  type: string;
  companyId: string | null;
  commissionType: string;
  value: number;
  isActive: boolean;
  effectiveFrom: string;
  effectiveTo: string | null;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

export interface EmployerCommissionSettingsResponse {
  statusCode: number;
  message: string;
  data: EmployerCommissionSettingsData;
  timestamp: string;
  path: string;
  method: string;
}

const EMPLOYER_COMMISSION_KEYS = {
  all: ['admin-settings', 'employer-commission'] as const,
  global: () => [...EMPLOYER_COMMISSION_KEYS.all, 'global'] as const,
};

// Fetch employer commission settings
const fetchEmployerCommissionSettings = async (): Promise<EmployerCommissionSettingsResponse> => {
  const response = await apiClient.get('/admin/settings/commission/global');

  return response as unknown as EmployerCommissionSettingsResponse;
};

export const useGetEmployerCommissionSettings = () => {
  return useQuery({
    queryKey: EMPLOYER_COMMISSION_KEYS.global(),
    queryFn: fetchEmployerCommissionSettings,
  });
};

export { EMPLOYER_COMMISSION_KEYS };
