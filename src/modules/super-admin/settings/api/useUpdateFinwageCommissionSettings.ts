import { apiClient } from '@/lib/api-client';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { FINWAGE_COMMISSION_KEYS } from './useGetFinwageCommissionSettings';

export interface UpdateFinwageCommissionSettingsPayload {
  type: string;
  value: number;
}

export interface UpdateFinwageCommissionSettingsData {
  id: string;
  key: string;
  config: {
    type: string;
    value: number;
    minAmount: number | null;
    maxAmount: number | null;
  };
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

export interface UpdateFinwageCommissionSettingsResponse {
  statusCode: number;
  message: string;
  data: UpdateFinwageCommissionSettingsData;
  timestamp: string;
  path: string;
  method: string;
}

// Update FinWage transaction fee settings
const updateFinwageCommissionSettings = async (
  payload: UpdateFinwageCommissionSettingsPayload
): Promise<UpdateFinwageCommissionSettingsResponse> => {
  const response = await apiClient.put('/admin/settings/transaction-fee', payload);

  return response as unknown as UpdateFinwageCommissionSettingsResponse;
};

export const useUpdateFinwageCommissionSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateFinwageCommissionSettings,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: FINWAGE_COMMISSION_KEYS.transactionFee(),
      });
    },
  });
};
