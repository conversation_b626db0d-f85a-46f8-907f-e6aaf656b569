import { apiClient } from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';

export type EmployeeStatus = 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
export type KycStatus = 'PENDING' | 'SUBMITTED' | 'VERIFIED' | 'REJECTED';
export type BankStatus = 'PENDING' | 'VERIFIED' | 'FAILED';

export interface Employee {
  id: string;
  fullName: string;
  email: string;
  phone: string;
  address: string;
  jobTitle: string;
  startDate: string;
  status: EmployeeStatus;
  kycStatus: KycStatus;
  bankStatus: BankStatus;
  companyId: string;
  company: {
    id: string;
    name: string;
  };
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName?: string;
  };
  createdAt: string;
  updatedAt: string;
  bankAccountSummary: {
    totalAccounts: number;
    verifiedAccounts: number;
    pendingAccounts: number;
    failedAccounts: number;
    primaryAccountId?: string;
    hasVerifiedAccount: boolean;
  };
}

export interface EmployerEmployeesResponse {
  statusCode: number;
  message: string;
  data: Employee[];
  meta: {
    total: number;
    lastPage: number;
    currentPage: number;
    perPage: number;
    prev: number | null;
    next: number | null;
  };
  activeEmployeesCount: number;
  suspendedEmployeesCount: number;
  timestamp: string;
  path: string;
  method: string;
}

export interface EmployerEmployeesFilters {
  page?: number;
  limit?: number;
  search?: string;
  status?: EmployeeStatus | 'all';
  kycStatus?: KycStatus | 'all';
}

const fetchEmployerEmployees = async (
  employerId: string,
  filters: EmployerEmployeesFilters = {}
): Promise<EmployerEmployeesResponse> => {
  const params = new URLSearchParams();

  // Add pagination parameters
  if (filters.page) {
    params.append('page', filters.page.toString());
  }
  if (filters.limit) {
    params.append('limit', filters.limit.toString());
  }

  // Add filter parameters
  if (filters.search && filters.search.trim()) {
    params.append('search', filters.search.trim());
  }
  if (filters.status && filters.status !== 'all') {
    params.append('status', filters.status);
  }
  if (filters.kycStatus && filters.kycStatus !== 'all') {
    params.append('kycStatus', filters.kycStatus);
  }

  const queryString = params.toString();
  const url = queryString
    ? `/employers/${employerId}/employees?${queryString}`
    : `/employers/${employerId}/employees`;

  const response = await apiClient.get(url);

  // The API client's response interceptor returns response.data,
  // so response is already the API response body
  return response as unknown as EmployerEmployeesResponse;
};

export const useEmployerEmployees = (
  employerId: string,
  filters: EmployerEmployeesFilters = {}
) => {
  return useQuery({
    queryKey: ['employer-employees', employerId, filters],
    queryFn: () => fetchEmployerEmployees(employerId, filters),
    enabled: !!employerId, // Only run query if employerId is provided
  });
};
