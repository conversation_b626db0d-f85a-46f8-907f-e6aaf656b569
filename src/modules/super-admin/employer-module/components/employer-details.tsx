'use client';

import { useState, useEffect } from 'react';
import {
  ArrowLeft,
  Building2,
  Mail,
  Phone,
  MapPin,
  Users,
  Calendar,
  Edit,
  Search,
  User,
  CheckCircle,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { toast } from '@/lib/toast';
import { useRouter } from 'nextjs-toploader/app';
import { CreateUpdateEmployerModal } from './create-update-employer-modal';
import { Employer } from '../api/useEmployers';
import { useQueryClient } from '@tanstack/react-query';
import Header from '@/components/reusable-component/header';
import { DataTable, type TableColumn } from '@/components/table-component/data-table';
import {
  useEmployerEmployees,
  type Employee,
  type EmployeeStatus,
  type KycStatus,
} from '../api/useEmployerEmployees';

interface EmployerDetailProps {
  employer: {
    id: string;
    companyName: string;
    contactPerson: string;
    email: string;
    status: string;
    phone: string;
    address: string;
    employeeCount: number;
    createdAt: string;
    updatedAt: string;
  };
  // Add the raw employer data for the modal
  rawEmployerData?: Employer;
}

export const EmployerDetail = ({ employer, rawEmployerData }: EmployerDetailProps) => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<EmployeeStatus | 'all'>('all');
  const [kycFilter, setKycFilter] = useState<KycStatus | 'all'>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [approvalDialogOpen, setApprovalDialogOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);

  // Debounce search term to avoid too many API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Fetch employees using the API hook
  const {
    data: employeesData,
    isLoading: isEmployeesLoading,
    error: employeesError,
  } = useEmployerEmployees(employer.id, {
    page: currentPage,
    limit: itemsPerPage,
    search: debouncedSearchTerm,
    status: statusFilter,
    kycStatus: kycFilter,
  });

  const employees = employeesData?.data || [];
  const totalItems = employeesData?.meta.total || 0;
  const totalPages = employeesData?.meta.lastPage || 1;

  const handleApproval = () => {
    setApprovalDialogOpen(true);
  };

  const confirmApproval = () => {
    // Here you would typically make an API call to approve the employer
    console.log(`Approving employer ${employer.id}`);

    toast.success(`${employer.companyName} has been approved successfully.`);

    // Close the dialog
    setApprovalDialogOpen(false);

    // In a real app, you'd update the state or refetch data and redirect
  };

  const handleEditEmployer = () => {
    setEditModalOpen(true);
  };

  const handleCloseEditModal = () => {
    setEditModalOpen(false);
  };

  const handleEmployerUpdated = () => {
    // Refresh the page or refetch data after successful update
    queryClient.invalidateQueries({ queryKey: ['employer-details', employer.id] });
  };

  // Reset pagination when filters change
  const handleFilterChange = () => {
    setCurrentPage(1);
  };

  const getEmployeeStatusBadge = (status: EmployeeStatus) => {
    switch (status) {
      case 'ACTIVE':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Active</Badge>;
      case 'SUSPENDED':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Suspended</Badge>;
      case 'INACTIVE':
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">Inactive</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getKycStatusBadge = (status: KycStatus) => {
    switch (status) {
      case 'VERIFIED':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Verified</Badge>;
      case 'PENDING':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Pending</Badge>;
      case 'SUBMITTED':
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Submitted</Badge>;
      case 'REJECTED':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Rejected</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getEmployerStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Active</Badge>;
      case 'suspended':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Suspended</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Pending</Badge>;
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Rejected</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Define table columns for employee list
  const employeeColumns: TableColumn<Employee>[] = [
    {
      key: 'employee',
      header: 'Employee',
      render: (employee) => (
        <div className="flex items-center gap-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src={`/placeholder.svg?height=40&width=40`} />
            <AvatarFallback className="bg-primary/10 text-primary font-medium">
              {employee.fullName
                .split(' ')
                .map((n: any) => n[0])
                .join('')}
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="font-medium text-gray-900">{employee.fullName}</div>
            <div className="text-sm text-gray-600">{employee.email}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'contact',
      header: 'Contact',
      render: (employee) => (
        <div className="space-y-1">
          <div className="flex items-center gap-2 text-sm text-gray-900">
            <Phone className="h-3 w-3" />
            {employee.phone}
          </div>
          <div className="line-clamp-1 flex items-start gap-2 overflow-hidden text-sm break-words text-gray-600">
            <MapPin className="mt-0.5 h-3 w-3 flex-shrink-0" />
            <span className="line-clamp-2">{employee.address}</span>
          </div>
        </div>
      ),
    },
    {
      key: 'jobTitle',
      header: 'Job Title',
      render: (employee) => <div className="text-gray-900">{employee.jobTitle}</div>,
    },
    {
      key: 'startDate',
      header: 'Start Date',
      render: (employee) => (
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-gray-400" />
          <span className="text-gray-900">{formatDate(employee.startDate)}</span>
        </div>
      ),
    },
    {
      key: 'status',
      header: 'Status',
      render: (employee) => getEmployeeStatusBadge(employee.status),
    },
    // {
    //   key: 'kycStatus',
    //   header: 'KYC Status',
    //   render: (employee) => getKycStatusBadge(employee.kycStatus),
    // },
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={() => router.back()} className="h-10 w-10">
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <Header
            title={employer.companyName}
            description="Company details and employee management"
          />
        </div>
        <div className="flex items-center gap-3">
          {/* {employer.status === 'pending' && (
            <Button onClick={handleApproval} className="h-10 bg-green-600 px-6 hover:bg-green-700">
              <CheckCircle className="mr-2 h-4 w-4" />
              Approve Employer
            </Button>
          )} */}
          <Button onClick={handleEditEmployer} className="bg-primary hover:bg-primary/90 h-10 px-6">
            <Edit className="mr-2 h-4 w-4" />
            Edit Company
          </Button>
        </div>
      </div>

      {/* Company Overview Section */}
      <div className="grid gap-8 lg:grid-cols-3">
        {/* Company Information - Takes 2 columns */}
        <div className="lg:col-span-2">
          <Card className="h-full">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-xl">
                <div className="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-lg">
                  <Building2 className="text-primary h-5 w-5" />
                </div>
                Company Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Status Row */}
              <div className="flex items-center justify-between border-b border-gray-100 py-3">
                <span className="text-sm font-medium text-gray-600">Status</span>
                {getEmployerStatusBadge(employer.status)}
              </div>

              {/* Contact Information Grid */}
              <div className="grid gap-6 sm:grid-cols-2">
                <div className="flex items-start gap-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-100">
                    <Mail className="h-4 w-4 text-gray-600" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium text-gray-900">Email Address</p>
                    <p className="truncate text-sm text-gray-600">{employer.email}</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-100">
                    <Phone className="h-4 w-4 text-gray-600" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium text-gray-900">Phone Number</p>
                    <p className="text-sm text-gray-600">{employer.phone}</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-100">
                    <User className="h-4 w-4 text-gray-600" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium text-gray-900">Contact Person</p>
                    <p className="text-sm text-gray-600">{employer.contactPerson}</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-100">
                    <Calendar className="h-4 w-4 text-gray-600" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium text-gray-900">Member Since</p>
                    <p className="text-sm text-gray-600">{formatDate(employer.createdAt)}</p>
                  </div>
                </div>
              </div>

              {/* Address - Full Width */}
              <div className="flex items-start gap-3 pt-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-100">
                  <MapPin className="h-4 w-4 text-gray-600" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-sm font-medium text-gray-900">Business Address</p>
                  <p className="text-sm leading-relaxed text-gray-600">{employer.address}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Employee Statistics - Takes 1 column */}
        <div className="lg:col-span-1">
          <Card className="h-full">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-xl">
                <div className="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-lg">
                  <Users className="text-primary h-5 w-5" />
                </div>
                Employee Overview
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Total Employees */}
              <div className="border-primary/20 from-primary/5 to-primary/10 rounded-xl border bg-gradient-to-br p-6 text-center">
                <div className="text-primary mb-1 text-3xl font-bold">
                  {employeesData?.meta?.total || 0}
                </div>
                <div className="text-sm font-medium text-gray-600">Total Employees</div>
              </div>

              {/* Active/Suspended Breakdown */}
              <div className="grid grid-cols-2 gap-4">
                <div className="rounded-lg border border-green-200 bg-green-50 p-4 text-center">
                  <div className="mb-1 text-2xl font-bold text-green-700">
                    {employeesData?.activeEmployeesCount || 0}
                  </div>
                  <div className="text-xs font-medium text-green-600">Active</div>
                </div>
                <div className="rounded-lg border border-gray-200 bg-gray-50 p-4 text-center">
                  <div className="mb-1 text-2xl font-bold text-gray-700">
                    {employeesData?.suspendedEmployeesCount || 0}
                  </div>
                  <div className="text-xs font-medium text-gray-600">Suspended</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Employee List Section */}
      <Card>
        <CardHeader className="pb-6">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl">Employee Directory</CardTitle>
              <CardDescription className="text-base">
                All employees under {employer.companyName}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="mb-6 flex flex-col gap-4 sm:flex-row">
            <div className="relative max-w-md flex-1">
              <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
              <Input
                placeholder="Search by name, email, or phone..."
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  handleFilterChange();
                }}
                className="h-10 pl-10"
              />
            </div>
            <div className="flex gap-3">
              <Select
                value={statusFilter}
                onValueChange={(value) => {
                  setStatusFilter(value as EmployeeStatus | 'all');
                  handleFilterChange();
                }}
              >
                <SelectTrigger className="!h-10 w-[140px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="ACTIVE">Active</SelectItem>
                  <SelectItem value="SUSPENDED">Suspended</SelectItem>
                  <SelectItem value="INACTIVE">Inactive</SelectItem>
                </SelectContent>
              </Select>
              {/* <Select
                value={kycFilter}
                onValueChange={(value) => {
                  setKycFilter(value as KycStatus | 'all');
                  handleFilterChange();
                }}
              >
                <SelectTrigger className="h-10 w-[140px]">
                  <SelectValue placeholder="KYC Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All KYC</SelectItem>
                  <SelectItem value="PENDING">Pending</SelectItem>
                  <SelectItem value="SUBMITTED">Submitted</SelectItem>
                  <SelectItem value="VERIFIED">Verified</SelectItem>
                  <SelectItem value="REJECTED">Rejected</SelectItem>
                </SelectContent>
              </Select> */}
            </div>
          </div>

          {/* DataTable */}
          <DataTable
            columns={employeeColumns}
            data={employees}
            meta={{
              total: totalItems,
              lastPage: totalPages,
              currentPage: currentPage,
            }}
            onPageChange={setCurrentPage}
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={(newItemsPerPage: number) => {
              setItemsPerPage(newItemsPerPage);
              setCurrentPage(1);
            }}
            isLoading={isEmployeesLoading}
            error={employeesError ? 'Error loading employees. Please try again.' : null}
            emptyState={{
              icon: <Users className="h-12 w-12 text-gray-400" />,
              title: 'No employees found',
              description: 'This employer has no employees yet.',
            }}
            getRowKey={(employee) => employee.id}
          />
        </CardContent>
      </Card>
      {/* Approval Confirmation Dialog */}
      <Dialog open={approvalDialogOpen} onOpenChange={setApprovalDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Approve Employer
            </DialogTitle>
            <DialogDescription asChild>
              <div>
                <p className="text-muted-foreground mb-4 text-sm">
                  Are you sure you want to approve <strong>{employer.companyName}</strong>?
                </p>
                <p className="text-muted-foreground mb-2 text-sm">This action will:</p>
                <ul className="text-muted-foreground mb-4 list-inside list-disc space-y-1 text-sm">
                  <li>Activate the employer account</li>
                  <li>Allow the employer to access all features</li>
                  <li>Enable employee management capabilities</li>
                  <li>Send approval notification to the employer</li>
                </ul>
                <p className="text-muted-foreground text-sm">This action cannot be undone.</p>
              </div>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="sm:justify-end">
            <Button variant="outline" onClick={() => setApprovalDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={confirmApproval} className="bg-green-600 hover:bg-green-700">
              <CheckCircle className="mr-2 h-4 w-4" />
              Approve Employer
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Employer Modal */}
      {rawEmployerData && (
        <CreateUpdateEmployerModal
          isOpen={editModalOpen}
          onClose={handleCloseEditModal}
          mode="edit"
          employerData={rawEmployerData}
          onEmployerCreated={handleEmployerUpdated}
        />
      )}
    </div>
  );
};
