export type Permission =
  | 'dashboard.view'
  | 'employers.view'
  | 'employers.create'
  | 'employers.edit'
  | 'employers.delete'
  | 'employees.view'
  | 'employees.view_details'
  | 'employees.create'
  | 'employees.edit'
  | 'employees.delete'
  | 'approvals.view'
  | 'approvals.approve'
  | 'approvals.reject'
  | 'analytics.view'
  | 'transactions.view'
  | 'transactions.manage'
  | 'commissions.view'
  | 'commissions.manage'
  | 'salary_requests.view'
  | 'salary_requests.approve'
  | 'salary_requests.reject'
  | 'wages.view'
  | 'wages_management.view'
  | 'wages.create'
  | 'wages.edit'
  | 'wages.delete'
  | 'repayments.view'
  | 'repayments.view_details'
  | 'repayments.export'
  | 'employer.settings.view'
  | 'settings.view'
  | 'settings.manage'
  | 'users.manage'
  | 'roles.manage'
  | 'roles.invite'
  | 'roles.view_profile'
  | 'roles.deactivate'
  | 'roles.revoke_invite'
  | 'invitations.view'
  | 'invitations.resend';

export type Role = 'super_admin' | 'employer' | 'sub_admin';

// Define permissions for each role
export const ROLE_PERMISSIONS: Record<Role, Permission[]> = {
  // Super Admin - REMOVED employer-specific permissions
  super_admin: [
    // 'dashboard.view',
    'employers.view',
    'employers.create',
    'employers.edit',
    'employers.delete',
    'approvals.view',
    'approvals.approve',
    'approvals.reject',
    'analytics.view',
    'transactions.view',
    'transactions.manage',
    'commissions.view',
    'commissions.manage',
    'settings.view',
    'settings.manage',
    'users.manage',
  ],
  employer: [
    'dashboard.view',
    'employees.view',
    'employees.view_details',
    'employees.create',
    'employees.edit',
    'employees.delete',
    'salary_requests.view',
    'salary_requests.approve',
    'salary_requests.reject',
    'wages.view',
    'wages.create',
    'wages.edit',
    'wages.delete',
    'repayments.view',
    'repayments.view_details',
    'repayments.export',
    'roles.manage',
    'roles.invite',
    'roles.view_profile',
    'roles.deactivate',
    'roles.revoke_invite',
    'wages_management.view',
    'employer.settings.view',
    'invitations.view',
    'invitations.resend',
  ],
  // Sub-admin now has all employer permissions
  sub_admin: [
    'dashboard.view',
    'employees.view',
    'employees.view_details',
    'employees.create',
    'employees.edit',
    'employees.delete',
    'salary_requests.view',
    'salary_requests.approve',
    'salary_requests.reject',
    'wages.view',
    'wages.create',
    'wages.edit',
    'wages.delete',
    'repayments.view',
    'repayments.view_details',
    'repayments.export',
    'roles.manage',
    'roles.invite',
    'roles.view_profile',
    'roles.deactivate',
    'roles.revoke_invite',
    'wages_management.view',
    'employer.settings.view',
    'invitations.view',
    'invitations.resend',
  ],
};

// Navigation items with required permissions
export interface NavItem {
  title: string;
  url: string;
  icon: any;
  key: string;
  requiredPermission: Permission;
  roles?: Role[]; // Optional: specific roles that can see this item
}

// Super Admin navigation items - Only super admin can see employers and admin features
export const SUPER_ADMIN_NAV_ITEMS: NavItem[] = [
  // {
  //   title: 'Dashboard',
  //   url: '/admin/dashboard',
  //   icon: 'Home',
  //   key: 'dashboard',
  //   requiredPermission: 'dashboard.view',
  // },
  {
    title: 'Employers',
    url: '/admin/employers',
    icon: 'Users',
    key: 'employers',
    requiredPermission: 'employers.view',
    roles: ['super_admin'], // Only super admin can see employers
  },
  {
    title: 'Approvals',
    url: '/admin/approvals',
    icon: 'CheckCircle',
    key: 'approvals',
    requiredPermission: 'approvals.view',
    roles: ['super_admin'],
  },
  {
    title: 'Analytics',
    url: '/admin/analytics',
    icon: 'BarChart3',
    key: 'analytics',
    requiredPermission: 'analytics.view',
    roles: ['super_admin'],
  },
  {
    title: 'Transactions',
    url: '/admin/transactions',
    icon: 'CreditCard',
    key: 'transactions',
    requiredPermission: 'transactions.view',
    roles: ['super_admin'],
  },
  {
    title: 'Commissions',
    url: '/admin/commissions',
    icon: 'DollarSign',
    key: 'commissions',
    requiredPermission: 'commissions.view',
    roles: ['super_admin'],
  },
  {
    title: 'Settings',
    url: '/admin/settings',
    icon: 'Settings',
    key: 'settings',
    requiredPermission: 'settings.view',
    roles: ['super_admin'],
  },
];

// Employer navigation items - No employers page for employer role
export const EMPLOYER_NAV_ITEMS: NavItem[] = [
  {
    title: 'Dashboard',
    url: '/employer/dashboard',
    icon: 'Home',
    key: 'employer-dashboard',
    requiredPermission: 'dashboard.view',
  },
  {
    title: 'Employees',
    url: '/employer/employees',
    icon: 'Users',
    key: 'employees',
    requiredPermission: 'employees.view',
  },
  {
    title: 'Wage Management',
    url: '/employer/wages',
    icon: 'Calculator',
    key: 'wages',
    requiredPermission: 'wages.view',
  },
  {
    title: 'Salary Requests',
    url: '/employer/salary-requests',
    icon: 'DollarSign',
    key: 'salary-requests',
    requiredPermission: 'salary_requests.view',
  },
  {
    title: 'Repayments',
    url: '/employer/repayments',
    icon: 'CreditCard',
    key: 'repayments',
    requiredPermission: 'repayments.view',
  },
  {
    title: 'Role Management',
    url: '/employer/role-management',
    icon: 'Shield',
    key: 'role-management',
    requiredPermission: 'roles.manage',
  },
  {
    title: 'Invites',
    url: '/employer/invites',
    icon: 'Mail',
    key: 'invites',
    requiredPermission: 'invitations.view',
  },
];

// Sub Admin navigation items - Same as employer (full access)
export const SUB_ADMIN_NAV_ITEMS: NavItem[] = [
  {
    title: 'Dashboard',
    url: '/employer/dashboard',
    icon: 'Home',
    key: 'employer-dashboard',
    requiredPermission: 'dashboard.view',
  },
  {
    title: 'Employees',
    url: '/employer/employees',
    icon: 'Users',
    key: 'employees',
    requiredPermission: 'employees.view',
  },
  {
    title: 'Salary Requests',
    url: '/employer/salary-requests',
    icon: 'DollarSign',
    key: 'salary-requests',
    requiredPermission: 'salary_requests.view',
  },
  {
    title: 'Wage Management',
    url: '/employer/wages',
    icon: 'Calculator',
    key: 'wages',
    requiredPermission: 'wages.view',
  },
  {
    title: 'Repayments',
    url: '/employer/repayments',
    icon: 'CreditCard',
    key: 'repayments',
    requiredPermission: 'repayments.view',
  },
  {
    title: 'Role Management',
    url: '/employer/role-management',
    icon: 'Shield',
    key: 'role-management',
    requiredPermission: 'roles.manage',
  },
  {
    title: 'Invites',
    url: '/employer/invites',
    icon: 'Mail',
    key: 'invites',
    requiredPermission: 'invitations.view',
  },
];

// Utility functions for permission checking
export class PermissionManager {
  private static instance: PermissionManager;
  private userRole: Role | null = null;
  private userPermissions: Permission[] = [];

  private constructor() {}

  static getInstance(): PermissionManager {
    if (!PermissionManager.instance) {
      PermissionManager.instance = new PermissionManager();
    }
    return PermissionManager.instance;
  }

  setUserRole(role: Role) {
    this.userRole = role;
    this.userPermissions = ROLE_PERMISSIONS[role] || [];
  }

  getUserRole(): Role | null {
    return this.userRole;
  }

  hasPermission(permission: Permission): boolean {
    return this.userPermissions.includes(permission);
  }

  hasAnyPermission(permissions: Permission[]): boolean {
    return permissions.some((permission) => this.hasPermission(permission));
  }

  hasAllPermissions(permissions: Permission[]): boolean {
    return permissions.every((permission) => this.hasPermission(permission));
  }

  canAccessRoute(requiredPermission: Permission, allowedRoles?: Role[]): boolean {
    if (!this.userRole) return false;

    // Check role-specific access first
    if (allowedRoles && !allowedRoles.includes(this.userRole)) {
      return false;
    }

    // Check permission
    return this.hasPermission(requiredPermission);
  }

  getFilteredNavItems(navItems: NavItem[]): NavItem[] {
    return navItems.filter((item) => this.canAccessRoute(item.requiredPermission, item.roles));
  }

  // Get appropriate nav items based on role
  getNavItemsForRole(): NavItem[] {
    switch (this.userRole) {
      case 'super_admin':
        return this.getFilteredNavItems(SUPER_ADMIN_NAV_ITEMS);
      case 'employer':
        return this.getFilteredNavItems(EMPLOYER_NAV_ITEMS);
      case 'sub_admin':
        return this.getFilteredNavItems(SUB_ADMIN_NAV_ITEMS);
      default:
        return [];
    }
  }

  // Check if user can access employer routes - PREVENT super_admin access
  canAccessEmployerRoutes(): boolean {
    return this.userRole === 'employer' || this.userRole === 'sub_admin';
  }

  // Future: Custom permission overrides for specific users
  addCustomPermission(permission: Permission) {
    if (!this.userPermissions.includes(permission)) {
      this.userPermissions.push(permission);
    }
  }

  removeCustomPermission(permission: Permission) {
    this.userPermissions = this.userPermissions.filter((p) => p !== permission);
  }
}

// Role management utilities
export class RoleManager {
  // Future: This could be connected to a backend API
  static assignRole(userId: string, role: Role): void {
    // In a real application, this would make an API call
    localStorage.setItem(`user_${userId}_role`, role);
  }

  static getUserRole(userId: string): Role | null {
    // In a real application, this would fetch from API
    return localStorage.getItem(`user_${userId}_role`) as Role | null;
  }

  static updateRolePermissions(role: Role, permissions: Permission[]): void {
    // Future: Allow dynamic role permission updates
    // This would typically be an admin-only function
    console.log(`Updating permissions for role ${role}:`, permissions);
    // In a real app, this would update the backend configuration
  }

  static createCustomRole(roleName: string, permissions: Permission[]): void {
    // Future: Support for custom roles
    console.log(`Creating custom role ${roleName} with permissions:`, permissions);
  }
}

// Hook for using permissions in components
export function usePermissions() {
  const permissionManager = PermissionManager.getInstance();

  return {
    hasPermission: (permission: Permission) => permissionManager.hasPermission(permission),
    hasAnyPermission: (permissions: Permission[]) =>
      permissionManager.hasAnyPermission(permissions),
    hasAllPermissions: (permissions: Permission[]) =>
      permissionManager.hasAllPermissions(permissions),
    canAccessRoute: (requiredPermission: Permission, allowedRoles?: Role[]) =>
      permissionManager.canAccessRoute(requiredPermission, allowedRoles),
    canAccessEmployerRoutes: () => permissionManager.canAccessEmployerRoutes(),
    getUserRole: () => permissionManager.getUserRole(),
  };
}
