import { apiClient } from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';
import { useAuthStore } from '@/store/auth-store';
import { useEffect } from 'react';

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  roles: string[];
  profilePicture?: string | { id: number; file_url: string };
}

interface Company {
  id: string;
  name: string;
  address: string;
  phoneNumber: string;
  status: 'PENDING' | 'ACTIVE' | 'INACTIVE'; // Add other statuses if needed
  createdAt: string; // Use `Date` if you plan to parse it
  updatedAt: string; // Use `Date` if you plan to parse it
}

export interface GetUserResponse {
  statusCode: number;
  message: string;
  data: {
    user: User;
    company: Company;
  };
  timestamp: string;
  path: string;
  method: string;
}

const getMe = (): Promise<GetUserResponse> => {
  return apiClient.get('/auth/me');
};

export const useGetMe = () => {
  const { setUserDetails, setCompanyDetails, authToken } = useAuthStore();

  const query = useQuery({
    queryKey: ['me'],
    queryFn: getMe,
    enabled: !!authToken, // Only fetch when authenticated
    staleTime: 10 * 60 * 1000, // 10 minutes stale time for user data
  });

  // Store user details and company details in auth store when data is successfully fetched
  useEffect(() => {
    if (query.data?.data?.user) {
      setUserDetails(query.data.data.user);
    }
    if (query.data?.data?.company) {
      setCompanyDetails(query.data.data.company);
    }
  }, [query.data, setUserDetails, setCompanyDetails]);

  return query;
};
