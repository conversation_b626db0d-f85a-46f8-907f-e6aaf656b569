import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';

export type UpdateProfilePayload = {
  firstName: string;
  lastName: string;
  companyName: string;
  phoneNumber: string;
  address: string;
  profilePicture?: {
    id: number;
    file_url: string;
  } | null;
};

export interface UpdateProfileResponse {
  statusCode: number;
  message: string;
  data: any;
  timestamp: string;
  path: string;
  method: string;
}

const updateProfileApi = async (payload: UpdateProfilePayload): Promise<UpdateProfileResponse> => {
  const response = await apiClient.patch('/auth/profile', payload);
  return response.data;
};

export const useUpdateProfile = () => {
  return useMutation({
    mutationFn: updateProfileApi,
  });
};
