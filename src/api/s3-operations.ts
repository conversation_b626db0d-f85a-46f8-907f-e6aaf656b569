import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import { toast } from '@/lib/toast';
import { AxiosError, AxiosProgressEvent } from 'axios';

interface FileUploadResponse {
  statusCode: number;
  message: string;
  data: {
    id: number;
    file_name: string;
    key: string;
    file_url: string;
    file_type: string;
    access_type: string;
  };
  timestamp: string;
  path: string;
  method: string;
}

interface UploadFileOptions {
  onProgress?: (progress: number) => void;
}

const uploadFile = async (file: File, options?: UploadFileOptions): Promise<FileUploadResponse> => {
  const formData = new FormData();
  formData.append('file', file);

  return apiClient.post('/file/upload/aws', formData, {
    onUploadProgress: (progressEvent: AxiosProgressEvent) => {
      if (options?.onProgress && progressEvent.total) {
        // Calculate the percentage
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        options.onProgress(percentCompleted);
      }
    },
  });
};

export const useUploadFile = () => {
  return useMutation({
    mutationFn: (params: { file: File; options?: UploadFileOptions }) =>
      uploadFile(params.file, params.options),
    onError: (error: AxiosError) => {
      toast.error(error);
    },
    retry: 1, // Only retry once for file uploads
  });
};
