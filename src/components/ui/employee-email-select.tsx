'use client';

import type React from 'react';
import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useInView } from 'react-intersection-observer';
import { Mail, ChevronDown, User, Check, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

// Custom debounce hook
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

export interface EmployeeEmailSelectOption {
  id: string;
  label: string;
  sublabel: string;
  avatar?: string;
}

interface EmployeeEmailSelectProps<T> {
  value: string;
  onValueChange: (value: string, item?: T) => void;
  onEmailChange: (email: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  error?: string;
  useQuery: (params: { page: number; limit: number; search: string }) => {
    data?: {
      data: T[];
      meta: { hasMore?: boolean; total?: number; lastPage?: number; currentPage?: number };
    };
    isLoading: boolean;
    error: any;
  };
  mapToOption: (item: T) => EmployeeEmailSelectOption;
  searchPlaceholder?: string;
  itemsPerPage?: number;
  icon?: React.ReactNode;
}

export function EmployeeEmailSelect<T>({
  value,
  onValueChange,
  onEmailChange,
  placeholder = 'Enter email or select employee',
  className,
  disabled = false,
  error,
  useQuery,
  mapToOption,
  searchPlaceholder = 'Search employees...',
  itemsPerPage = 10,
  icon = <Mail className="h-4 w-4" />,
}: EmployeeEmailSelectProps<T>) {
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState('');
  const debouncedSearch = useDebounce(search, 500);
  const [page, setPage] = useState(1);
  const [allItems, setAllItems] = useState<T[]>([]);
  const [selectedEmployee, setSelectedEmployee] = useState<T | null>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Memoize mapToOption callback to prevent infinite re-renders
  const stableMapToOption = useCallback((item: T) => mapToOption(item), [mapToOption]);

  // Use the search term from debounced search for API query
  const {
    data,
    isLoading,
    error: queryError,
  } = useQuery({
    page,
    limit: itemsPerPage,
    search: debouncedSearch,
  });

  // Reset pagination when search changes
  useEffect(() => {
    setPage(1);
    setAllItems([]);
  }, [debouncedSearch]);

  // Handle data loading and pagination
  useEffect(() => {
    if (data?.data) {
      if (page === 1) {
        setAllItems(data.data);
      } else {
        // For subsequent pages, only add if the data is not already present
        setAllItems((prev) => {
          const existingIds = new Set(prev.map((item) => stableMapToOption(item).id));
          const newItems = data.data.filter((item) => !existingIds.has(stableMapToOption(item).id));
          return [...prev, ...newItems];
        });
      }
    }
  }, [data?.data, page, stableMapToOption]); // Now using stableMapToOption

  // Use intersection observer for infinite scroll
  const { ref: intersectionRef, inView } = useInView({
    threshold: 0.1,
    rootMargin: '20px',
  });

  // Handle inView changes for pagination
  useEffect(() => {
    if (
      inView &&
      data?.meta &&
      ((data.meta.lastPage && page < data.meta.lastPage) || data.meta.hasMore) &&
      !isLoading
    ) {
      setPage((prev) => prev + 1);
    }
  }, [inView, data?.meta, page, isLoading]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setOpen(false);
      }
    };

    if (open) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [open]);

  const isValidEmail = useCallback((email: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  }, []);

  // Memoize the matching employee calculation
  const matchingEmployee = useMemo(() => {
    return allItems.find((item) => {
      const option = stableMapToOption(item);
      return option.sublabel.toLowerCase() === value.toLowerCase().trim();
    });
  }, [allItems, value, stableMapToOption]);

  // Update selected employee when value changes externally (but only if we find a match)
  useEffect(() => {
    if (matchingEmployee && selectedEmployee !== matchingEmployee) {
      setSelectedEmployee(matchingEmployee);
    } else if (!matchingEmployee && selectedEmployee) {
      // Only clear if user is actively changing input
      setSelectedEmployee(null);
    }
  }, [matchingEmployee, selectedEmployee]);

  const handleSelectItem = useCallback(
    (item: T) => {
      const option = stableMapToOption(item);
      setSelectedEmployee(item);
      setSearch(''); // Clear search after selection
      setOpen(false);

      // Update the main input with selected employee's email
      // and trigger the callbacks to notify parent component
      onValueChange(option.sublabel, item);
      onEmailChange(option.sublabel);
    },
    [stableMapToOption, onValueChange, onEmailChange]
  );

  const handleOpenChange = useCallback((newOpen: boolean) => {
    setOpen(newOpen);
    if (newOpen && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  }, []);

  const handleChevronClick = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setOpen(!open);
    },
    [open]
  );

  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;
      onValueChange(newValue, undefined);
      onEmailChange(newValue);

      // Clear selected employee if input doesn't match
      if (selectedEmployee) {
        const option = stableMapToOption(selectedEmployee);
        if (option.sublabel !== newValue) {
          setSelectedEmployee(null);
        }
      }
    },
    [onValueChange, onEmailChange, selectedEmployee, stableMapToOption]
  );

  const hasMorePages = useMemo(() => {
    return data?.meta && ((data.meta.lastPage && page < data.meta.lastPage) || data.meta.hasMore);
  }, [data?.meta, page]);

  return (
    <div ref={dropdownRef} className={cn('relative', className)}>
      <div className="space-y-2">
        {/* Main Input Field */}
        <div className="relative">
          <div className="absolute top-1/2 left-3 z-10 -translate-y-1/2 text-gray-400">{icon}</div>
          <Input
            type="email"
            placeholder={placeholder}
            value={value}
            onChange={handleInputChange}
            className={cn(
              'pr-10 pl-10',
              error && 'border-red-500',
              selectedEmployee && 'border-green-500'
            )}
            disabled={disabled}
          />
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="absolute top-1/2 right-1 h-6 w-6 -translate-y-1/2 p-0"
            onClick={handleChevronClick}
            disabled={disabled}
          >
            <ChevronDown className="h-4 w-4" />
          </Button>
        </div>

        {/* Employee Selection Dropdown */}
        {open && (
          <div className="absolute top-full right-0 left-0 z-50 mt-1 rounded-md border border-gray-200 bg-white shadow-lg">
            {/* Search Input */}
            <div className="flex items-center border-b px-3 py-2">
              <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <Input
                ref={searchInputRef}
                placeholder={searchPlaceholder}
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="h-8 border-0 !shadow-none focus-visible:ring-0 focus-visible:ring-offset-0"
                autoFocus
              />
            </div>

            {/* Results */}
            <div className="h-[300px] overflow-y-auto">
              <div className="p-1">
                {isLoading && page === 1 ? (
                  // Initial loading state
                  <div className="space-y-1">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <div key={i} className="flex items-center space-x-2 p-2">
                        <Skeleton className="h-8 w-8 rounded-full" />
                        <div className="flex-1 space-y-1">
                          <Skeleton className="h-4 w-3/4" />
                          <Skeleton className="h-3 w-1/2" />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : queryError ? (
                  <div className="text-muted-foreground p-4 text-center text-sm">
                    Error loading employees. Please try again.
                  </div>
                ) : allItems.length === 0 && debouncedSearch ? (
                  <div className="p-4">
                    <div className="text-center">
                      <User className="mx-auto mb-2 h-8 w-8 text-gray-400" />
                      <div className="text-sm font-medium text-gray-900">No employees found</div>
                      {isValidEmail(value) ? (
                        <div className="mt-1 text-xs text-gray-500">
                          Will send invitation to: <span className="font-medium">{value}</span>
                        </div>
                      ) : (
                        <div className="mt-1 text-xs text-red-500">
                          Please enter a valid email address
                        </div>
                      )}
                    </div>
                  </div>
                ) : allItems.length === 0 ? (
                  <div className="text-muted-foreground p-4 text-center text-sm">
                    {searchPlaceholder}
                  </div>
                ) : (
                  <>
                    {allItems.map((item) => {
                      const option = stableMapToOption(item);
                      const isSelected =
                        selectedEmployee && stableMapToOption(selectedEmployee).id === option.id;

                      return (
                        <div
                          key={option.id}
                          className={cn(
                            'flex cursor-pointer items-center gap-3 rounded-sm p-2 hover:bg-gray-100 hover:text-gray-900',
                            isSelected && 'bg-gray-100 text-gray-900'
                          )}
                          onClick={() => handleSelectItem(item)}
                        >
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={option.avatar} alt={option.label} />
                            <AvatarFallback className="text-xs">
                              {option.label
                                .split(' ')
                                .map((n) => n[0])
                                .join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div className="min-w-0 flex-1">
                            <div className="truncate text-sm font-medium">{option.label}</div>
                            <div className="truncate text-xs text-gray-500">{option.sublabel}</div>
                          </div>
                          {isSelected && <Check className="h-4 w-4 text-green-600" />}
                        </div>
                      );
                    })}

                    {/* Load more trigger */}
                    {hasMorePages && (
                      <div ref={intersectionRef} className="p-2">
                        {isLoading ? (
                          <div className="flex items-center justify-center">
                            <div className="flex items-center space-x-2">
                              <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-gray-400"></div>
                              <span className="text-sm text-gray-500">Loading more...</span>
                            </div>
                          </div>
                        ) : (
                          <div className="text-center text-sm text-gray-500">
                            Scroll for more options
                          </div>
                        )}
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {error && <p className="mt-1 text-sm text-red-600">{error}</p>}

      {/* Status indicator */}
      {value && (
        <div className="mt-2">
          {selectedEmployee ? (
            <div className="flex items-center gap-1 text-xs text-green-600">
              <Check className="h-3 w-3" />
              Employee found: {stableMapToOption(selectedEmployee).label}
            </div>
          ) : isValidEmail(value) ? (
            <div className="flex items-center gap-1 text-xs text-blue-600">
              <Mail className="h-3 w-3" />
              New user invitation: {value}
            </div>
          ) : value.length > 0 ? (
            <div className="text-xs text-red-500">Please enter a valid email address</div>
          ) : null}
        </div>
      )}
    </div>
  );
}
