'use client';

import { useState, useRef } from 'react';
import { Camera, Loader2, X } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from './avatar';
import { Button } from './button';
import { cn } from '@/lib/utils';

interface AvatarUploadProps {
  /** Current avatar image URL */
  avatarUrl?: string;
  /** Fallback text when no image is available */
  fallbackText?: string;
  /** Called when a file is selected for upload */
  onFileSelect?: (file: File) => void;
  /** Called when the remove button is clicked */
  onRemove?: () => void;
  /** Whether upload is in progress */
  isUploading?: boolean;
  /** Upload progress percentage (0-100) */
  uploadProgress?: number;
  /** Size of the avatar */
  size?: 'sm' | 'md' | 'lg' | 'xl';
  /** Whether the component is disabled */
  disabled?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Accept file types */
  accept?: string;
}

const sizeClasses = {
  sm: 'h-12 w-12',
  md: 'h-16 w-16',
  lg: 'h-20 w-20',
  xl: 'h-24 w-24',
};

const cameraSizeClasses = {
  sm: 'h-4 w-4',
  md: 'h-5 w-5',
  lg: 'h-6 w-6',
  xl: 'h-7 w-7',
};

export function AvatarUpload({
  avatarUrl,
  fallbackText = 'U',
  onFileSelect,
  onRemove,
  isUploading = false,
  uploadProgress = 0,
  size = 'md',
  disabled = false,
  className,
  accept = 'image/*',
}: AvatarUploadProps) {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('File size must be less than 5MB');
      return;
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }

    // Create preview URL
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);

    // Call the callback
    onFileSelect?.(file);

    // Reset input
    event.target.value = '';
  };

  const handleCameraClick = () => {
    if (disabled || isUploading) return;
    fileInputRef.current?.click();
  };

  const handleRemove = () => {
    setPreviewUrl(null);
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
    onRemove?.();
  };

  const displayUrl = previewUrl || avatarUrl;
  const showRemoveButton = (previewUrl || avatarUrl) && !isUploading;

  return (
    <div className={cn('relative inline-block', className)}>
      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        onChange={handleFileChange}
        className="hidden"
        disabled={disabled || isUploading}
      />

      {/* Avatar with overlay */}
      <div className="relative">
        <Avatar className={cn(sizeClasses[size], 'border-2 border-gray-200')}>
          <AvatarImage src={displayUrl} className="object-cover" />
          <AvatarFallback className="bg-primary font-semibold text-white">
            {fallbackText}
          </AvatarFallback>
        </Avatar>

        {/* Upload progress overlay */}
        {isUploading && (
          <div className="absolute inset-0 flex items-center justify-center rounded-full bg-black/50">
            <div className="text-center">
              <Loader2 className="mx-auto mb-1 h-6 w-6 animate-spin text-white" />
              <div className="text-xs font-medium text-white">{uploadProgress}%</div>
            </div>
          </div>
        )}

        {/* Camera button */}
        {!isUploading && (
          <Button
            type="button"
            size="sm"
            variant="default"
            className={cn(
              'bg-primary hover:bg-primary/90 absolute -right-1 -bottom-1 h-8 w-8 rounded-full border-2 border-white text-white shadow-lg',
              disabled && 'cursor-not-allowed opacity-50'
            )}
            onClick={handleCameraClick}
            disabled={disabled}
          >
            <Camera className={cameraSizeClasses[size]} />
          </Button>
        )}

        {/* Remove button */}
        {showRemoveButton && (
          <Button
            type="button"
            size="sm"
            variant="destructive"
            className={cn(
              'absolute -top-1 -right-1 h-6 w-6 rounded-full border-2 border-white p-0 shadow-lg'
            )}
            onClick={handleRemove}
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>
    </div>
  );
}
